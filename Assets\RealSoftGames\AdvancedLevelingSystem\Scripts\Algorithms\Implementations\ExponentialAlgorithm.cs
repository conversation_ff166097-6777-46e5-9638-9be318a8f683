using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Exponential leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Exponential Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Exponential Algorithm", order = 122)]
    public class ExponentialAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Base multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.2f)]
        public float zeroBaseMultiplier = 1.05f;

        [Tooltip("Level multiplier factor (higher values create steeper curves)")]
        [Range(0.001f, 0.05f)]
        public float levelMultiplierCoefficient = 0.01f;

        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Exponential";
            description = "Experience requirements grow exponentially, making higher levels significantly more challenging.";
            formulaExplanation = "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 + 0.01 * level))\n\nExperience requirements grow exponentially, making higher levels significantly more challenging.";

            // Call base implementation
            base.OnEnable();
        }

        /// <summary>
        /// Calculates the next experience requirement using the exponential formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);

            // Calculate the exponential growth factor
            // This creates a curve that increases the multiplier as levels increase
            float growthFactor = 1f + levelMultiplierCoefficient * currentLevel;

            // Calculate the actual multiplier with exponential growth
            float actualMultiplier;

            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure exponential pattern
                // with a smaller base to avoid excessive growth
                actualMultiplier = zeroBaseMultiplier * growthFactor;
            }
            else
            {
                // Apply the exponential growth to the effective multiplier
                actualMultiplier = effectiveMultiplier * growthFactor;
            }

            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);

            // Allow negative multipliers to reduce XP requirements (easier levels)
            // Only enforce a minimum of 1 XP to prevent zero or negative XP requirements
            return Mathf.Max(nextRequirement, 1);
        }

        /// <summary>
        /// Calculates raw formula values for visualization using the exponential formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();

            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);

            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the exponential growth factor
                float growthFactor = 1f + levelMultiplierCoefficient * level;

                // Calculate the actual multiplier with exponential growth
                float rawValue;

                if (Mathf.Approximately(levelUpMultiplier, 0f))
                {
                    // When levelUpMultiplier is exactly zero, use a pure exponential pattern
                    rawValue = zeroBaseMultiplier * growthFactor;
                }
                else if (levelUpMultiplier > 0f && levelUpMultiplier < 1.0f)
                {
                    // For multipliers between 0 and 1, we need special handling
                    // Scale between 1.0 and the zero base multiplier based on the levelUpMultiplier
                    float baseMultiplier = Mathf.Lerp(1.0f, zeroBaseMultiplier, levelUpMultiplier);
                    rawValue = baseMultiplier * growthFactor;
                }
                else
                {
                    // Apply the exponential growth to the effective multiplier
                    rawValue = effectiveMultiplier * growthFactor;
                }

                // Add to the list
                rawValues.Add(rawValue);
            }

            return rawValues;
        }
    }
}
