%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 91143bfbb917c974e864b58989dbb1e0, type: 3}
  m_Name: Stepped
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 00f1de9d-53b0-48b6-ac22-8445ff29ef68
  algorithmName: Stepped
  description: Creates plateaus of difficulty with sudden jumps at specific level
    thresholds, making progression feel like distinct tiers or ranks.
  formulaExplanation: 'Formula: Uses higher multiplier at specific level thresholds


    Creates
    plateaus of difficulty with sudden jumps at specific level thresholds, making
    progression feel like distinct tiers or ranks.'
  difficultyRating: {fileID: 6917784849675779520, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1.1
  cachedRequirementCurvePoints: []
  cachedRawFormulaCurvePoints: []
  cachedRequirementCurve: 
  cachedRawFormulaCurve: []
  stepSize: 5
  stepThresholdMultiplier: 1.5
  betweenStepsMultiplier: 0.8
  zeroMultiplierStepThreshold: 1.15
  zeroMultiplierBetweenSteps: 1.03
