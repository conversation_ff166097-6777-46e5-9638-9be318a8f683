%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: fd42e085fc663e24796061beca1b75f5, type: 3}
  m_Name: ExperienceRewardsData
  m_EditorClassIdentifier: 
  rewardCategories:
  - name: Epic Quest
    multiplier: 0.12
    hardValue: 0
    rewardType: 0
  - name: Hard Quest
    multiplier: 0.08
    hardValue: 0
    rewardType: 0
  - name: Medium Quest
    multiplier: 0.05
    hardValue: 0
    rewardType: 0
  - name: Easy Quest
    multiplier: 0.03
    hardValue: 0
    rewardType: 0
  - name: Daily Activity
    multiplier: 0.02
    hardValue: 0
    rewardType: 0
  - name: World Boss
    multiplier: 0.1
    hardValue: 0
    rewardType: 0
  - name: Boss Enemy
    multiplier: 0.05
    hardValue: 0
    rewardType: 0
  - name: Elite Enemy
    multiplier: 0.015
    hardValue: 0
    rewardType: 0
  - name: Hard Enemy
    multiplier: 0.003
    hardValue: 0
    rewardType: 0
  - name: Medium Enemy
    multiplier: 0.002
    hardValue: 0
    rewardType: 0
  - name: Easy Enemy
    multiplier: 0.001
    hardValue: 0
    rewardType: 0
  - name: Very Easy Enemy
    multiplier: 0.0005
    hardValue: 0
    rewardType: 0
  - name: Crafting Item
    multiplier: 0.0001
    hardValue: 0
    rewardType: 0
  - name: Gathering Resource
    multiplier: 0.0001
    hardValue: 0
    rewardType: 0
  selectedAlgorithm: {fileID: 11400000, guid: 86a336ac8af25c24ca391d0a9cf72d29, type: 2}
  defaultRewardType: 0
  levelUpMultiplier: 1.1
  startingExperience: 1250
  startingLevel: 1
  maxLevel: 50
  levelRangeSize: 5
  showDetailedBreakdown: 1
  showLevelCurvePreview: 0
