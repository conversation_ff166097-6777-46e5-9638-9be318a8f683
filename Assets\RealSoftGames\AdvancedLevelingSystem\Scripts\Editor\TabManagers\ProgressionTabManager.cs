using UnityEditor;
using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using System;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Combined tab manager for experience rewards and progression simulation
    /// </summary>
    public class ProgressionTabManager : ITabManager
    {
        #region Fields and Properties
        // Reference to the main editor window
        private readonly AdvancedLevelingSystemEditorWindow window;

        // Data references
        private ExperienceRewardsData rewardsData;

        // Scroll positions for each panel
        private Vector2 leftScrollPosition;
        private Vector2 rightScrollPosition;

        // Algorithm selection
        private LevelingAlgorithmBase selectedAlgorithm;

        // Tab properties
        public string TabName => "Progression";

        // Use shared settings from the window
        private float LevelUpMultiplier => window.SharedLevelUpMultiplier;
        private int StartingExperience => window.SharedStartingExperience;
        private int StartingLevel => window.SharedStartingLevel;
        private int MaxLevel => window.SharedMaxLevel;
        private int levelRangeSize = 5;
        private bool showDetailedBreakdown = true;
        private bool showLevelCurvePreview = true;

        // Graph visualization
        private LevelingCurvePreview curvePreview;

        // Reward categories and their multipliers (stored as decimal values, displayed as percentages)
        private Dictionary<string, float> rewardCategories = new Dictionary<string, float>();

        // Hard value reward categories (stored as base XP values)
        private Dictionary<string, int> hardValueRewardCategories = new Dictionary<string, int>();

        // Current reward type mode
        private RewardType currentRewardType = RewardType.Percentage;

        // Cached experience requirements
        private List<int> experienceRequirements;

        // Simulation settings
        private enum PlayerType { Casual, Regular, Hardcore }
        private PlayerType selectedPlayerType = PlayerType.Regular;

        // Player activity settings
        private float playTimePerDay = 2.0f; // hours
        private float actionsPerHour = 600.0f; // 10 per minute * 60 minutes
        private float xpPerAction = 1.0f;
        private SimulationMode simulationMode = SimulationMode.Simple;

        // XP Scaling settings
        private bool useXpScaling = true;
        private float xpScalingFactor = 1.05f; // 5% increase per level
        private XpScalingMode xpScalingMode = XpScalingMode.Linear;

        // Simulation results
        private List<SimulationResult> simulationResults;

        // Styles
        private GUIStyle headerStyle;
        private GUIStyle subHeaderStyle;
        private GUIStyle labelStyle;
        private GUIStyle valueStyle;
        private GUIStyle categoryHeaderStyle;
        private GUIStyle levelRangeStyle;
        private GUIStyle tooltipStyle;
        private GUIStyle sectionBoxStyle;

        // Right panel styles (cached to avoid creating every frame)
        private GUIStyle darkBackgroundStyle;
        private GUIStyle rightPanelHeaderStyle;
        private GUIStyle darkHeaderBackgroundStyle;
        private GUIStyle greenHeaderStyle;
        private GUIStyle levelProgressionHeaderStyle;
        private GUIStyle compactButtonStyle;
        private GUIStyle barTextStyle;

        // Level range foldout states
        private Dictionary<int, bool> levelRangeFoldoutStates = new Dictionary<int, bool>();
        private GUIStyle foldoutStyle;

        // Cached textures to avoid creating every frame
        private Texture2D darkBackgroundTexture;
        private Texture2D darkHeaderTexture;
        private Texture2D greenHeaderTexture;
        private Texture2D blueButtonTexture;

        // Cached player type comparison data
        private Dictionary<PlayerType, float> cachedPlayerTypeDays = new Dictionary<PlayerType, float>();
        private bool playerTypeComparisonDirty = true;

        // Cached reward preview data
        private Dictionary<string, Dictionary<int, int>> cachedRewardPreview = new Dictionary<string, Dictionary<int, int>>();
        private bool rewardPreviewDirty = true;

        // Panel widths
        private float leftPanelWidth;
        private float rightPanelWidth;
        #endregion

        // Simulation result class
        private class SimulationResult
        {
            public int Level { get; set; }
            public int ExperienceRequired { get; set; }
            public float HoursToLevel { get; set; }
            public float DaysToLevel { get; set; }
            public float TotalDays { get; set; }
            public float TotalHours { get; set; }
            public DateTime EstimatedDate { get; set; }
        }

        // Player type settings class
        private class PlayerTypeSettings
        {
            public float playTimePerDay;
            public float actionsPerHour;
            public float xpPerAction;

            public PlayerTypeSettings(float playTime, float actions, float xpPerAction = 1.0f)
            {
                playTimePerDay = playTime;
                actionsPerHour = actions;
                this.xpPerAction = xpPerAction;
            }
        }

        // Simulation mode for player progression
        private enum SimulationMode
        {
            Simple,   // Use preset player types
            Advanced  // Customize all parameters
        }

        // XP scaling mode for reward progression
        private enum XpScalingMode
        {
            Linear,      // XP rewards increase linearly with level
            Exponential, // XP rewards increase exponentially with level
            Logarithmic  // XP rewards increase logarithmically with level
        }

        // Dictionary of settings for each player type
        private Dictionary<PlayerType, PlayerTypeSettings> playerTypeSettings = new Dictionary<PlayerType, PlayerTypeSettings>();

        public ProgressionTabManager(AdvancedLevelingSystemEditorWindow window, ExperienceRewardsData rewardsData)
        {
            this.window = window;
            this.rewardsData = rewardsData;

            // Initialize curve preview
            curvePreview = new LevelingCurvePreview();

            // Initialize player type settings
            InitializePlayerTypeSettings();

            // Load data
            LoadData();
        }

        private void InitializePlayerTypeSettings()
        {
            // Clear existing settings
            playerTypeSettings.Clear();

            // Add default settings for each player type
            playerTypeSettings[PlayerType.Casual] = new PlayerTypeSettings(2.0f, 300.0f, 1.0f); // 5 actions per minute * 60
            playerTypeSettings[PlayerType.Regular] = new PlayerTypeSettings(4.0f, 600.0f, 1.0f); // 10 actions per minute * 60
            playerTypeSettings[PlayerType.Hardcore] = new PlayerTypeSettings(6.0f, 900.0f, 1.0f); // 15 actions per minute * 60

            // Set initial values based on selected player type
            UpdatePlayerTypeSettings();
        }

        private void UpdatePlayerTypeSettings()
        {
            if (playerTypeSettings.TryGetValue(selectedPlayerType, out PlayerTypeSettings settings))
            {
                playTimePerDay = settings.playTimePerDay;
                actionsPerHour = settings.actionsPerHour;
                xpPerAction = settings.xpPerAction;
            }
        }

        public void OnEnable()
        {
            LoadData();
            CalculateExperienceRequirements();
            RunSimulation();
        }

        public void OnDisable()
        {
            // No need to track enabled state - managed by the editor window
        }

        public void OnDestroy()
        {
        }

        public void Update()
        {
        }

        private void InitializeStyles()
        {
            if (headerStyle == null)
            {
                headerStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 16,
                    alignment = TextAnchor.MiddleLeft,
                    margin = new RectOffset(5, 5, 5, 5)
                };
            }

            if (subHeaderStyle == null)
            {
                subHeaderStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 14,
                    alignment = TextAnchor.MiddleLeft,
                    margin = new RectOffset(5, 5, 5, 5)
                };
            }

            if (labelStyle == null)
            {
                labelStyle = new GUIStyle(EditorStyles.label)
                {
                    fontSize = 12,
                    alignment = TextAnchor.MiddleLeft
                };
            }

            if (valueStyle == null)
            {
                valueStyle = new GUIStyle(EditorStyles.label)
                {
                    fontSize = 12,
                    alignment = TextAnchor.MiddleRight,
                    fontStyle = FontStyle.Bold
                };
            }

            if (categoryHeaderStyle == null)
            {
                categoryHeaderStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 14,
                    alignment = TextAnchor.MiddleLeft,
                    margin = new RectOffset(0, 0, 10, 5)
                };
            }

            if (levelRangeStyle == null)
            {
                levelRangeStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 13,
                    alignment = TextAnchor.MiddleCenter,
                    normal = { textColor = Color.white }
                };
            }

            if (tooltipStyle == null)
            {
                tooltipStyle = new GUIStyle(EditorStyles.helpBox)
                {
                    fontSize = 12,
                    wordWrap = true,
                    richText = true,
                    padding = new RectOffset(10, 10, 10, 10)
                };
            }

            if (sectionBoxStyle == null)
            {
                sectionBoxStyle = new GUIStyle(EditorStyles.helpBox)
                {
                    padding = new RectOffset(10, 10, 10, 10),
                    margin = new RectOffset(0, 0, 5, 5)
                };
            }

            if (foldoutStyle == null)
            {
                foldoutStyle = new GUIStyle(EditorStyles.foldout)
                {
                    fontStyle = FontStyle.Bold,
                    fontSize = 12,
                    normal = { textColor = Color.white }
                };
            }

            // Initialize cached textures
            if (darkBackgroundTexture == null)
                darkBackgroundTexture = CreateColorTexture(new Color(0.2f, 0.2f, 0.2f));
            if (darkHeaderTexture == null)
                darkHeaderTexture = CreateColorTexture(new Color(0.15f, 0.15f, 0.3f));
            if (greenHeaderTexture == null)
                greenHeaderTexture = CreateColorTexture(new Color(0.2f, 0.4f, 0.2f));
            if (blueButtonTexture == null)
                blueButtonTexture = CreateColorTexture(new Color(0.2f, 0.4f, 0.8f));

            // Initialize right panel styles
            if (darkBackgroundStyle == null)
            {
                darkBackgroundStyle = new GUIStyle();
                darkBackgroundStyle.normal.background = darkBackgroundTexture;
            }

            if (rightPanelHeaderStyle == null)
            {
                rightPanelHeaderStyle = new GUIStyle(EditorStyles.boldLabel);
                rightPanelHeaderStyle.alignment = TextAnchor.MiddleCenter;
                rightPanelHeaderStyle.fontSize = 12;
                rightPanelHeaderStyle.normal.textColor = Color.white;
                rightPanelHeaderStyle.margin = new RectOffset(0, 0, 6, 6);
                rightPanelHeaderStyle.padding = new RectOffset(4, 4, 4, 4);
            }

            if (darkHeaderBackgroundStyle == null)
            {
                darkHeaderBackgroundStyle = new GUIStyle();
                darkHeaderBackgroundStyle.normal.background = darkHeaderTexture;
            }

            if (greenHeaderStyle == null)
            {
                greenHeaderStyle = new GUIStyle(rightPanelHeaderStyle);
                greenHeaderStyle.normal.background = greenHeaderTexture;
            }

            if (levelProgressionHeaderStyle == null)
            {
                levelProgressionHeaderStyle = new GUIStyle(EditorStyles.boldLabel)
                {
                    fontSize = 13,
                    alignment = TextAnchor.MiddleCenter,
                    normal = { textColor = Color.white },
                    stretchWidth = true
                };
            }

            if (compactButtonStyle == null && Event.current != null)
            {
                compactButtonStyle = new GUIStyle(GUI.skin.button)
                {
                    fontSize = 10,
                    fontStyle = FontStyle.Bold,
                    padding = new RectOffset(3, 3, 2, 2),
                    normal = { textColor = Color.white, background = blueButtonTexture }
                };
            }

            if (barTextStyle == null)
            {
                barTextStyle = new GUIStyle(EditorStyles.boldLabel);
                barTextStyle.normal.textColor = Color.white;
                barTextStyle.alignment = TextAnchor.MiddleRight;
            }
        }

        public void OnGUI()
        {
            InitializeStyles();

            // Set a minimum window width to prevent horizontal scrollbars
            float minWindowWidth = 1000f; // Minimum width for the entire window
            if (window.position.width < minWindowWidth)
            {
                EditorGUILayout.HelpBox($"Please resize the window to at least {minWindowWidth}px width for optimal viewing.", MessageType.Info);
            }

            // Two-panel layout
            EditorGUILayout.BeginHorizontal();

            // Calculate the available width (accounting for the left navigation panel)
            float availableWidth = EditorWindowTemplate.MainBodyRect.width;

            // Left panel (Algorithm & Settings + Reward Categories) - 50% of available width
            leftPanelWidth = (availableWidth - 10) * 0.49f;
            EditorGUILayout.BeginVertical(GUILayout.Width(leftPanelWidth), GUILayout.ExpandHeight(true));

            // Begin scrollview for the entire left panel
            leftScrollPosition = EditorGUILayout.BeginScrollView(
                leftScrollPosition,
                false, // No horizontal scrollbar
                true,  // Vertical scrollbar
                GUILayout.ExpandWidth(true),
                GUILayout.ExpandHeight(true));

            // Draw Algorithm & Settings section
            DrawAlgorithmSettings();

            // Add a separator between sections
            EditorGUILayout.Space(10);
            Rect horizontalSeparator = EditorGUILayout.GetControlRect(false, 2);
            EditorGUI.DrawRect(horizontalSeparator, new Color(0.5f, 0.5f, 0.5f, 0.5f));
            EditorGUILayout.Space(10);

            // Draw Reward Categories section
            DrawRewardCategories();

            EditorGUILayout.EndScrollView();
            EditorGUILayout.EndVertical();

            // Add a visual separator between panels (thinner)
            GUILayout.Space(5);
            Rect verticalSeparator = GUILayoutUtility.GetRect(1, EditorWindowTemplate.MainBodyRect.height);
            EditorGUI.DrawRect(verticalSeparator, new Color(0.4f, 0.4f, 0.4f, 0.4f));
            GUILayout.Space(5);

            // Right panel (Level Progression & Simulation) - 50% of available width
            rightPanelWidth = (availableWidth - 20) * 0.49f;
            EditorGUILayout.BeginVertical(GUILayout.Width(rightPanelWidth), GUILayout.ExpandHeight(true));
            DrawRightPanel();
            EditorGUILayout.EndVertical();

            // Add a small space at the end to prevent horizontal scrollbar
            GUILayout.Space(2);
            EditorGUILayout.EndHorizontal();
        }

        #region Data Management
        private void LoadData()
        {
            // Try to load the default rewards data if not provided
            if (rewardsData == null)
            {
                rewardsData = Resources.Load<ExperienceRewardsData>("DefaultExperienceRewardsData");

                // If still null, create a new instance with default values
                if (rewardsData == null)
                {
                    // Initialize with default reward categories
                    InitializeDefaultRewardCategories();
                    return;
                }
            }

            // Load the algorithm if available
            selectedAlgorithm = rewardsData.selectedAlgorithm;

            // If no algorithm is selected, try to find one in the database
            if (selectedAlgorithm == null)
            {
                AlgorithmDatabase algorithmDB = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
                if (algorithmDB != null && algorithmDB.algorithms != null && algorithmDB.algorithms.Count > 0)
                {
                    // Try to find a SineWave algorithm or use the first one
                    selectedAlgorithm = algorithmDB.algorithms.Find(a => a != null && a.Name != null && a.Name.ToLower().Contains("sine"));
                    if (selectedAlgorithm == null)
                    {
                        selectedAlgorithm = algorithmDB.algorithms[0];
                    }

                    // Update the rewards data with the selected algorithm
                    if (rewardsData != null)
                    {
                        rewardsData.selectedAlgorithm = selectedAlgorithm;
                        EditorUtility.SetDirty(rewardsData);
                    }
                }
            }

            // Note: levelUpMultiplier, startingExperience, startingLevel, and maxLevel are now shared settings
            // and are accessed through properties that get values from the window
            levelRangeSize = rewardsData.levelRangeSize;
            showDetailedBreakdown = rewardsData.showDetailedBreakdown;
            showLevelCurvePreview = rewardsData.showLevelCurvePreview;

            // Update the shared settings in the window
            window.UpdateSharedSettings(
                levelUpMultiplier: rewardsData.levelUpMultiplier,
                startingExperience: rewardsData.startingExperience,
                startingLevel: rewardsData.startingLevel,
                maxLevel: rewardsData.maxLevel
            );

            // Load reward categories and current reward type
            rewardCategories.Clear();
            hardValueRewardCategories.Clear();
            currentRewardType = rewardsData.defaultRewardType;

            if (rewardsData.rewardCategories != null && rewardsData.rewardCategories.Count > 0)
            {
                foreach (var category in rewardsData.rewardCategories)
                {
                    if (category.rewardType == RewardType.Percentage)
                    {
                        rewardCategories[category.name] = category.multiplier;
                    }
                    else
                    {
                        hardValueRewardCategories[category.name] = category.hardValue;
                    }
                }
            }
            else
            {
                // Initialize with default reward categories if none exist
                InitializeDefaultRewardCategories();
            }
        }

        private void InitializeDefaultRewardCategories()
        {
            // Clear existing categories
            rewardCategories.Clear();
            hardValueRewardCategories.Clear();

            if (currentRewardType == RewardType.Percentage)
            {
                // Add default reward categories with their multipliers in the desired order
                // Quest categories
                rewardCategories.Add("Epic Quest", 0.12f);
                rewardCategories.Add("Hard Quest", 0.08f);
                rewardCategories.Add("Medium Quest", 0.05f);
                rewardCategories.Add("Easy Quest", 0.03f);
                rewardCategories.Add("Daily Activity", 0.02f);

                // Boss and special enemies
                rewardCategories.Add("World Boss", 0.10f);
                rewardCategories.Add("Boss Enemy", 0.05f);
                rewardCategories.Add("Elite Enemy", 0.015f);

                // Regular enemies
                rewardCategories.Add("Hard Enemy", 0.003f);
                rewardCategories.Add("Medium Enemy", 0.002f);
                rewardCategories.Add("Easy Enemy", 0.001f);
                rewardCategories.Add("Very Easy Enemy", 0.0005f);

                // Crafting and gathering
                rewardCategories.Add("Crafting Item", 0.0001f);
                rewardCategories.Add("Gathering Resource", 0.0001f);
            }
            else
            {
                // Add default hard value reward categories (base XP values that scale with level)
                // Quest categories
                hardValueRewardCategories.Add("Epic Quest", 300);
                hardValueRewardCategories.Add("Hard Quest", 200);
                hardValueRewardCategories.Add("Medium Quest", 125);
                hardValueRewardCategories.Add("Easy Quest", 75);
                hardValueRewardCategories.Add("Daily Activity", 50);

                // Boss and special enemies
                hardValueRewardCategories.Add("World Boss", 250);
                hardValueRewardCategories.Add("Boss Enemy", 125);
                hardValueRewardCategories.Add("Elite Enemy", 40);

                // Regular enemies
                hardValueRewardCategories.Add("Hard Enemy", 8);
                hardValueRewardCategories.Add("Medium Enemy", 5);
                hardValueRewardCategories.Add("Easy Enemy", 3);
                hardValueRewardCategories.Add("Very Easy Enemy", 1);

                // Crafting and gathering
                hardValueRewardCategories.Add("Crafting Item", 1);
                hardValueRewardCategories.Add("Gathering Resource", 1);
            }

            // If we have a rewards data object, update it
            if (rewardsData != null)
            {
                UpdateRewardCategoriesInScriptableObject();
            }
        }

        private void UpdateRewardCategoriesInScriptableObject()
        {
            if (rewardsData != null)
            {
                // Clear and update reward categories
                rewardsData.rewardCategories.Clear();
                rewardsData.defaultRewardType = currentRewardType;

                if (currentRewardType == RewardType.Percentage)
                {
                    foreach (var category in rewardCategories)
                    {
                        rewardsData.rewardCategories.Add(new ExperienceRewardsData.RewardCategory
                        {
                            name = category.Key,
                            multiplier = category.Value,
                            rewardType = RewardType.Percentage
                        });
                    }
                }
                else
                {
                    foreach (var category in hardValueRewardCategories)
                    {
                        rewardsData.rewardCategories.Add(new ExperienceRewardsData.RewardCategory
                        {
                            name = category.Key,
                            hardValue = category.Value,
                            rewardType = RewardType.HardValue
                        });
                    }
                }

                // Mark the asset as dirty so Unity knows it needs to be saved
                EditorUtility.SetDirty(rewardsData);
                AssetDatabase.SaveAssets();
            }
        }

        private void SaveData()
        {
            if (rewardsData == null)
            {
                return;
            }

            // Update the algorithm
            rewardsData.selectedAlgorithm = selectedAlgorithm;

            rewardsData.levelUpMultiplier = LevelUpMultiplier;
            rewardsData.startingExperience = StartingExperience;
            rewardsData.startingLevel = StartingLevel;
            rewardsData.maxLevel = MaxLevel;
            rewardsData.levelRangeSize = levelRangeSize;
            rewardsData.showDetailedBreakdown = showDetailedBreakdown;
            rewardsData.showLevelCurvePreview = showLevelCurvePreview;

            // Update reward categories
            UpdateRewardCategoriesInScriptableObject();
        }
        #endregion

        #region Helper Methods
        /// <summary>
        /// Creates a texture with a solid color
        /// </summary>
        private Texture2D CreateColorTexture(Color color)
        {
            Texture2D texture = new Texture2D(1, 1);
            texture.SetPixel(0, 0, color);
            texture.Apply();
            return texture;
        }



        /// <summary>
        /// Displays a level range with its header and progression breakdown
        /// </summary>
        private void DisplayLevelRangeWithRewards(int startLevel, int rangeSize, int averageExp, GUIStyle headerTextStyle)
        {
            // Calculate the end level (clamped to max level)
            int endLevel = Mathf.Min(startLevel + rangeSize - 1, MaxLevel);

            // Get or initialize the foldout state for this level range
            if (!levelRangeFoldoutStates.ContainsKey(startLevel))
            {
                levelRangeFoldoutStates[startLevel] = true; // Default to expanded
            }

            // Create a header rect for the level range
            Rect levelRangeHeaderRect = EditorGUILayout.GetControlRect(false, 30);
            EditorGUI.DrawRect(levelRangeHeaderRect, new Color(0.15f, 0.15f, 0.3f));

            // Display the level range with foldout
            string levelRangeText = $"Level {startLevel} - {endLevel}";

            // Create a rect for the foldout arrow
            Rect foldoutRect = new Rect(levelRangeHeaderRect.x + 10, levelRangeHeaderRect.y, 20, levelRangeHeaderRect.height);

            // Create a rect for the level range text - adjust width to avoid overlap with time text
            Rect labelRect = new Rect(levelRangeHeaderRect.x + 30, levelRangeHeaderRect.y, levelRangeHeaderRect.width - 230, levelRangeHeaderRect.height);

            // Handle the foldout click
            bool wasExpanded = levelRangeFoldoutStates[startLevel];
            levelRangeFoldoutStates[startLevel] = EditorGUI.Foldout(foldoutRect, wasExpanded, "", true, foldoutStyle);

            // Display the level range text
            EditorGUI.LabelField(labelRect, levelRangeText, headerTextStyle);

            // Add time estimation to the header if we have simulation results
            if (simulationResults != null && simulationResults.Count > 0)
            {
                // Find the simulation result for this level range
                SimulationResult startResult = simulationResults.Find(r => r.Level == startLevel);
                SimulationResult endResult = simulationResults.Find(r => r.Level == endLevel + 1) ?? simulationResults.LastOrDefault();

                if (startResult != null && endResult != null)
                {
                    // Calculate time difference
                    float totalDays = endResult.TotalDays - startResult.TotalDays;

                    // Create a rect for the time text (right-aligned) - wider to accommodate the longer text
                    Rect timeRect = new Rect(
                        levelRangeHeaderRect.x + levelRangeHeaderRect.width - 220,
                        levelRangeHeaderRect.y,
                        210,
                        levelRangeHeaderRect.height);

                    // Format time text with a clear label
                    string timeText;
                    if (totalDays < 1)
                    {
                        float hours = totalDays * 24;
                        timeText = $"Time to complete: ~{hours:F1} hours";
                    }
                    else if (totalDays < 30)
                    {
                        timeText = $"Time to complete: ~{totalDays:F1} days";
                    }
                    else
                    {
                        float months = totalDays / 30f;
                        timeText = $"Time to complete: ~{months:F1} months";
                    }

                    // Display the time text
                    GUIStyle timeStyle = new GUIStyle(headerTextStyle);
                    timeStyle.alignment = TextAnchor.MiddleRight;
                    EditorGUI.LabelField(timeRect, timeText, timeStyle);
                }
            }

            // Only show content if expanded
            if (levelRangeFoldoutStates[startLevel])
            {
                // Create a background for the content that expands to fill width
                EditorGUILayout.BeginVertical(EditorStyles.helpBox, GUILayout.ExpandWidth(true));

                // Display the average experience
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Average XP Required:", subHeaderStyle);
                EditorGUILayout.LabelField(averageExp.ToString("N0") + " XP", valueStyle);
                EditorGUILayout.EndHorizontal();

                // Display detailed breakdown
                EditorGUILayout.Space(5);

                // Graph removed as it was not working well

                // Draw time estimation for each level if simulation results are available
                if (simulationResults != null && simulationResults.Count > 0)
                {
                    EditorGUILayout.LabelField("Time Estimation", subHeaderStyle);

                    // Table header with consistent widths
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField("Level", subHeaderStyle, GUILayout.Width(50));

                    // Create right-aligned header styles
                    GUIStyle timeHeaderStyle = new GUIStyle(subHeaderStyle) { alignment = TextAnchor.MiddleRight };

                    EditorGUILayout.LabelField("Time to Level", timeHeaderStyle, GUILayout.Width(100));
                    EditorGUILayout.LabelField("Total Time", timeHeaderStyle, GUILayout.Width(100));
                    EditorGUILayout.EndHorizontal();

                    // Draw a separator line
                    Rect timeEstimationSeparatorRect = EditorGUILayout.GetControlRect(false, 1);
                    EditorGUI.DrawRect(timeEstimationSeparatorRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));

                    // Display time estimation for each level in the range
                    for (int level = startLevel; level <= endLevel; level++)
                    {
                        SimulationResult result = simulationResults.Find(r => r.Level == level);
                        if (result != null)
                        {
                            EditorGUILayout.BeginHorizontal();

                            // Level
                            EditorGUILayout.LabelField(level.ToString(), labelStyle, GUILayout.Width(50));

                            // Time to level
                            string timeToLevel;
                            if (level == StartingLevel)
                                timeToLevel = "-";
                            else if (result.DaysToLevel < 1)
                                timeToLevel = $"{result.HoursToLevel:F1} hours";
                            else
                                timeToLevel = $"{result.DaysToLevel:F1} days";

                            GUIStyle rightAlignedStyle = new GUIStyle(valueStyle) { alignment = TextAnchor.MiddleRight };
                            EditorGUILayout.LabelField(timeToLevel, rightAlignedStyle, GUILayout.Width(100));

                            // Total time
                            string totalTime;
                            if (level == StartingLevel)
                                totalTime = "-";
                            else if (result.TotalDays < 1)
                                totalTime = $"{result.TotalHours:F1} hours";
                            else if (result.TotalDays < 30)
                                totalTime = $"{result.TotalDays:F1} days";
                            else if (result.TotalDays < 365)
                                totalTime = $"{result.TotalDays / 30f:F1} months";
                            else
                                totalTime = $"{result.TotalDays / 365f:F1} years";

                            EditorGUILayout.LabelField(totalTime, rightAlignedStyle, GUILayout.Width(100));

                            EditorGUILayout.EndHorizontal();
                        }
                    }

                    EditorGUILayout.Space(5);
                }

                // Show detailed breakdown only if enabled
                if (showDetailedBreakdown)
                {
                    EditorGUILayout.LabelField("Level Progression Breakdown", subHeaderStyle);

                    // Table header with consistent widths
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField("Level", subHeaderStyle, GUILayout.Width(50));

                    // Create right-aligned header style for Required XP
                    GUIStyle rightAlignedLevelHeaderStyle = new GUIStyle(subHeaderStyle);
                    rightAlignedLevelHeaderStyle.alignment = TextAnchor.MiddleRight;

                    EditorGUILayout.LabelField("Req XP", rightAlignedLevelHeaderStyle, GUILayout.Width(80));
                    EditorGUILayout.LabelField("Inc %", rightAlignedLevelHeaderStyle, GUILayout.Width(60));
                    EditorGUILayout.EndHorizontal();

                    // Draw a separator line
                    Rect separatorRect = EditorGUILayout.GetControlRect(false, 1);
                    EditorGUI.DrawRect(separatorRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));

                    // Display each level in the range
                    int startIdx = Mathf.Max(0, startLevel - StartingLevel);
                    int endIdx = Mathf.Min(experienceRequirements.Count - 1, startIdx + rangeSize - 1);

                    for (int i = startIdx; i <= endIdx; i++)
                    {
                        if (i < experienceRequirements.Count)
                        {
                            EditorGUILayout.BeginHorizontal();

                            // Level number
                            EditorGUILayout.LabelField((i + StartingLevel).ToString(), labelStyle, GUILayout.Width(50));

                            // Required XP - ensure it's right-aligned
                            // Create a dedicated style for the XP values with right alignment
                            GUIStyle xpValueStyle = new GUIStyle(valueStyle);
                            xpValueStyle.alignment = TextAnchor.MiddleRight;
                            EditorGUILayout.LabelField(experienceRequirements[i].ToString("N0"), xpValueStyle, GUILayout.Width(80));

                            // Percentage increase from previous level
                            if (i > 0)
                            {
                                float increase = ((float)experienceRequirements[i] / experienceRequirements[i - 1] - 1) * 100;
                                string increaseText = increase.ToString("0.0") + "%";

                                // Color code the increase percentage
                                GUIStyle increaseStyle = new GUIStyle(valueStyle);
                                increaseStyle.alignment = TextAnchor.MiddleRight; // Right-align the increase percentage
                                if (increase < 5)
                                    increaseStyle.normal.textColor = new Color(0.0f, 0.8f, 0.0f); // Green for small increases
                                else if (increase > 15)
                                    increaseStyle.normal.textColor = new Color(0.8f, 0.0f, 0.0f); // Red for large increases

                                EditorGUILayout.LabelField(increaseText, increaseStyle, GUILayout.Width(60));
                            }
                            else
                            {
                                EditorGUILayout.LabelField("-", GUILayout.Width(60));
                            }

                            EditorGUILayout.EndHorizontal();
                        }
                    }
                }

                // Add a section for rewards
                EditorGUILayout.Space(10);

                // Create a header rect for the rewards section
                Rect rewardsHeaderRect = EditorGUILayout.GetControlRect(false, 30);
                EditorGUI.DrawRect(rewardsHeaderRect, new Color(0.2f, 0.4f, 0.2f));

                // Display the rewards header with scaling indicator
                string rewardsHeaderText = "Rewards (Algorithm-Scaled)";
                EditorGUI.LabelField(rewardsHeaderRect, rewardsHeaderText, headerTextStyle);

                EditorGUILayout.Space(5);

                // Calculate total experience needed for this level range
                int totalExpForRange = 0;
                int rangeStartIdx = Mathf.Max(0, startLevel - StartingLevel);
                int rangeEndIdx = Mathf.Min(experienceRequirements.Count - 1, rangeStartIdx + rangeSize - 1);

                for (int i = rangeStartIdx; i <= rangeEndIdx; i++)
                {
                    if (i < experienceRequirements.Count)
                    {
                        totalExpForRange += experienceRequirements[i];
                    }
                }

                // Display total experience needed for this level range
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Total XP Needed:", subHeaderStyle);
                EditorGUILayout.LabelField(totalExpForRange.ToString("N0") + " XP", valueStyle);
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.Space(5);

                // Table header with fixed widths to ensure proper layout
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Activity", subHeaderStyle, GUILayout.Width(100));

                // Create right-aligned header style for XP Reward and % of Level
                GUIStyle rightAlignedHeaderStyle = new GUIStyle(subHeaderStyle)
                {
                    alignment = TextAnchor.MiddleRight
                };

                EditorGUILayout.LabelField("XP", rightAlignedHeaderStyle, GUILayout.Width(60));
                EditorGUILayout.LabelField("% Level", rightAlignedHeaderStyle, GUILayout.Width(60));
                EditorGUILayout.LabelField("Need", rightAlignedHeaderStyle, GUILayout.Width(60));
                EditorGUILayout.EndHorizontal();

                // Draw a separator line
                Rect rewardsSeparatorRect = EditorGUILayout.GetControlRect(false, 1);
                EditorGUI.DrawRect(rewardsSeparatorRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));

                // Sort categories by reward amount (descending) based on current reward type
                if (currentRewardType == RewardType.Percentage)
                {
                    var sortedPercentageCategories = rewardCategories.OrderByDescending(x => x.Value).ToList();
                    foreach (var category in sortedPercentageCategories)
                    {
                        DisplayCategoryReward(category.Key, startLevel, rangeSize, averageExp, labelStyle, valueStyle);
                    }
                }
                else
                {
                    var sortedHardValueCategories = hardValueRewardCategories.OrderByDescending(x => x.Value).ToList();
                    foreach (var category in sortedHardValueCategories)
                    {
                        DisplayCategoryReward(category.Key, startLevel, rangeSize, averageExp, labelStyle, valueStyle);
                    }
                }

                EditorGUILayout.EndVertical();
            }
        }

        private void DisplayCategoryReward(string categoryName, int startLevel, int rangeSize, int averageExp, GUIStyle labelStyle, GUIStyle valueStyle)
        {
            // Get the average level for this range
            int avgLevel = startLevel + (rangeSize / 2);

            // Calculate the scaled reward based on the level using the unified method
            int rewardAmount = CalculateExperienceRewardForCategory(categoryName, averageExp, avgLevel);

            // Calculate the effective percentage for display
            float percentOfLevel;
            if (currentRewardType == RewardType.Percentage)
            {
                float effectiveMultiplier = CalculateScaledRewardMultiplier(avgLevel, rewardCategories[categoryName]);
                percentOfLevel = effectiveMultiplier * 100;
            }
            else
            {
                // For hard values, calculate percentage based on actual reward vs required XP
                percentOfLevel = averageExp > 0 ? (rewardAmount / (float)averageExp) * 100 : 0;
            }

            // Calculate how many of this reward type are needed to level up
            // Use the scaled reward amount for accurate calculation
            int numNeeded = rewardAmount > 0 ? Mathf.CeilToInt(averageExp / (float)rewardAmount) : 0;

            EditorGUILayout.BeginHorizontal();

            // Activity type
            EditorGUILayout.LabelField(categoryName, labelStyle, GUILayout.Width(100));

            // XP reward - ensure it's right-aligned
            GUIStyle xpRewardStyle = new GUIStyle(valueStyle)
            {
                alignment = TextAnchor.MiddleRight
            };
            EditorGUILayout.LabelField(FormatLargeNumber(rewardAmount), xpRewardStyle, GUILayout.Width(60));

            // Percentage of level
            GUIStyle percentStyle = new GUIStyle(valueStyle)
            {
                alignment = TextAnchor.MiddleRight // Right-align the percentage
            };

            // Format percentage with appropriate precision
            string percentFormat = percentOfLevel < 0.01f ? "F3" : (percentOfLevel < 0.1f ? "F2" : "F1");
            EditorGUILayout.LabelField(percentOfLevel.ToString(percentFormat) + "%", percentStyle, GUILayout.Width(60));

            // Number needed to level up
            GUIStyle numNeededStyle = new GUIStyle(valueStyle)
            {
                alignment = TextAnchor.MiddleRight
            };
            EditorGUILayout.LabelField(FormatCompactNumber(numNeeded), numNeededStyle, GUILayout.Width(60));

            EditorGUILayout.EndHorizontal();
        }

        private void DisplayTotalNeededForRange(int startLevel, int rangeSize, int totalExpForRange, GUIStyle labelStyle, GUIStyle valueStyle)
        {
            EditorGUILayout.Space(5);
            EditorGUILayout.LabelField("Activities Needed to Complete Range", subHeaderStyle);
            EditorGUILayout.Space(2);

            // Table header for range totals
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Activity", subHeaderStyle, GUILayout.Width(100));

            // Create right-aligned header style
            GUIStyle rightAlignedHeaderStyle = new GUIStyle(subHeaderStyle)
            {
                alignment = TextAnchor.MiddleRight
            };
            EditorGUILayout.LabelField("# Required", rightAlignedHeaderStyle, GUILayout.Width(80));
            EditorGUILayout.EndHorizontal();

            // Draw a separator line
            Rect rangeTotalsSeparatorRect = EditorGUILayout.GetControlRect(false, 1);
            EditorGUI.DrawRect(rangeTotalsSeparatorRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));

            if (currentRewardType == RewardType.Percentage)
            {
                var sortedCategories = rewardCategories.OrderByDescending(x => x.Value).ToList();
                foreach (var category in sortedCategories)
                {
                    DisplayRangeTotalForCategory(category.Key, startLevel, rangeSize, totalExpForRange, labelStyle, valueStyle);
                }
            }
            else
            {
                var sortedCategories = hardValueRewardCategories.OrderByDescending(x => x.Value).ToList();
                foreach (var category in sortedCategories)
                {
                    DisplayRangeTotalForCategory(category.Key, startLevel, rangeSize, totalExpForRange, labelStyle, valueStyle);
                }
            }
        }

        private void DisplayRangeTotalForCategory(string categoryName, int startLevel, int rangeSize, int totalExpForRange, GUIStyle labelStyle, GUIStyle valueStyle)
        {
            // Get the average level for this range
            int avgLevel = startLevel + (rangeSize / 2);

            // Calculate the scaled reward based on the level using the unified method
            int averageExp = totalExpForRange / rangeSize; // Average XP per level in range
            int rewardAmount = CalculateExperienceRewardForCategory(categoryName, averageExp, avgLevel);

            // Calculate total needed for the entire range
            int totalNeeded = rewardAmount > 0 ? Mathf.CeilToInt(totalExpForRange / (float)rewardAmount) : 0;

            EditorGUILayout.BeginHorizontal();

            // Activity type
            EditorGUILayout.LabelField(categoryName, labelStyle, GUILayout.Width(100));

            // Total needed for range
            GUIStyle totalNeededStyle = new GUIStyle(valueStyle)
            {
                alignment = TextAnchor.MiddleRight,
                fontSize = 12,
                fontStyle = FontStyle.Bold
            };

            // Color code based on total needed
            if (totalNeeded <= 10)
                totalNeededStyle.normal.textColor = new Color(0.0f, 0.7f, 0.0f); // Green for few needed
            else if (totalNeeded >= 100000)
                totalNeededStyle.normal.textColor = new Color(0.7f, 0.0f, 0.0f); // Red for many needed
            else if (totalNeeded >= 50000)
                totalNeededStyle.normal.textColor = new Color(0.9f, 0.6f, 0.0f); // Orange for moderate

            EditorGUILayout.LabelField(FormatLargeNumber(totalNeeded), totalNeededStyle, GUILayout.Width(80));

            EditorGUILayout.EndHorizontal();
        }
        #endregion

        #region Helper Methods
        /// <summary>
        /// Formats large numbers for display in the UI
        /// </summary>
        private string FormatLargeNumber(int number)
        {
            if (number >= 1000000)
                return $"{number / 1000000.0:0.0}M";
            else if (number >= 1000)
                return $"{number / 1000.0:0.0}K";
            else
                return number.ToString();
        }

        /// <summary>
        /// Formats large numbers for display in the UI (float version)
        /// </summary>
        private string FormatLargeNumber(float number)
        {
            if (number >= 1000000)
                return $"{number / 1000000.0:0.0}M";
            else if (number >= 1000)
                return $"{number / 1000.0:0.0}K";
            else
                return number.ToString("0");
        }

        /// <summary>
        /// Formats numbers compactly for display in narrow columns
        /// </summary>
        private string FormatCompactNumber(int number)
        {
            if (number >= 1000000)
                return $"{number / 1000000.0:0.0}M";
            else if (number >= 1000)
                return $"{number / 1000.0:0.0}K";
            else
                return number.ToString();
        }
        #endregion

        #region Calculation Methods
        /// <summary>
        /// Calculates the reward multiplier based on the player's level with XP scaling
        /// </summary>
        private float CalculateScaledRewardMultiplier(int level, float baseMultiplier)
        {
            // Apply XP scaling if enabled
            if (useXpScaling)
            {
                float scalingMultiplier = CalculateXpScalingMultiplier(level);
                return baseMultiplier * scalingMultiplier;
            }

            // For accurate simulation and back-testing, we simply use the base multiplier
            // This ensures rewards are a consistent percentage of the level's XP requirement
            // which is what the user expects when setting up reward categories
            return baseMultiplier;
        }

        /// <summary>
        /// Calculates the XP scaling multiplier for a given level
        /// </summary>
        private float CalculateXpScalingMultiplier(int level)
        {
            float levelProgress = (float)(level - StartingLevel) / Mathf.Max(1, MaxLevel - StartingLevel);

            switch (xpScalingMode)
            {
                case XpScalingMode.Linear:
                    return 1.0f + (levelProgress * (xpScalingFactor - 1.0f));

                case XpScalingMode.Exponential:
                    // Use a much more conservative exponential scaling to prevent extreme values
                    // Cap the maximum multiplier to prevent unrealistic rewards
                    float expMultiplier = Mathf.Pow(xpScalingFactor, levelProgress);
                    return Mathf.Min(expMultiplier, 10.0f); // Cap at 10x maximum

                case XpScalingMode.Logarithmic:
                    return 1.0f + Mathf.Log(1.0f + levelProgress * (xpScalingFactor - 1.0f));

                default:
                    return 1.0f;
            }
        }

        /// <summary>
        /// Calculates the experience reward for a given category and level
        /// </summary>
        /// <param name="categoryPercentage">The base percentage of the level requirement</param>
        /// <param name="expRequired">The experience required for the level</param>
        /// <param name="level">The player's current level</param>
        /// <returns>The calculated experience reward (minimum 1)</returns>
        private int CalculateExperienceReward(float categoryPercentage, int expRequired, int level)
        {
            // Validate inputs to prevent negative or invalid values
            if (expRequired <= 0)
            {
                Debug.LogWarning($"Invalid experience requirement: {expRequired} at level {level}. Using minimum value of 1.");
                expRequired = 1;
            }

            if (categoryPercentage <= 0)
            {
                Debug.LogWarning($"Invalid category percentage: {categoryPercentage} at level {level}. Using minimum value of 0.001.");
                categoryPercentage = 0.001f;
            }

            // Get the scaled multiplier based on the player's level
            float scaledMultiplier = CalculateScaledRewardMultiplier(level, categoryPercentage);

            // Validate the scaled multiplier
            if (scaledMultiplier <= 0 || float.IsNaN(scaledMultiplier) || float.IsInfinity(scaledMultiplier))
            {
                Debug.LogWarning($"Invalid scaled multiplier: {scaledMultiplier} at level {level}. Using base percentage.");
                scaledMultiplier = categoryPercentage;
            }

            // Calculate the raw reward
            float rawReward = expRequired * scaledMultiplier;

            // Cap extremely large rewards to prevent UI issues
            const float MAX_REWARD = 1000000f; // 1 million XP max per action
            if (rawReward > MAX_REWARD)
            {
                Debug.LogWarning($"Extremely large reward calculated: {rawReward} at level {level}. Capping to {MAX_REWARD}.");
                rawReward = MAX_REWARD;
            }

            // Ensure the reward is at least 1, even for very small percentages (like 0.001%)
            // This ensures that even tiny percentage values will always grant at least 1 XP
            return Mathf.Max(1, Mathf.RoundToInt(rawReward));
        }

        /// <summary>
        /// Calculates the experience reward for a given category and level (supports both percentage and hard value modes)
        /// </summary>
        /// <param name="categoryName">The name of the reward category</param>
        /// <param name="expRequired">The experience required for the level</param>
        /// <param name="level">The player's current level</param>
        /// <returns>The calculated experience reward (minimum 1)</returns>
        private int CalculateExperienceRewardForCategory(string categoryName, int expRequired, int level)
        {
            if (currentRewardType == RewardType.Percentage)
            {
                if (rewardCategories.TryGetValue(categoryName, out float categoryPercentage))
                {
                    return CalculateExperienceReward(categoryPercentage, expRequired, level);
                }
            }
            else
            {
                if (hardValueRewardCategories.TryGetValue(categoryName, out int baseHardValue))
                {
                    // Calculate level-based scaling for hard values
                    float levelScaling = CalculateHardValueLevelScaling(level);
                    float scaledReward = baseHardValue * levelScaling;

                    // Apply XP scaling if enabled
                    if (useXpScaling)
                    {
                        float xpScalingMultiplier = CalculateXpScalingMultiplier(level);
                        scaledReward *= xpScalingMultiplier;
                    }

                    // Cap extremely large rewards to prevent UI issues
                    const float MAX_REWARD = 1000000f; // 1 million XP max per action
                    if (scaledReward > MAX_REWARD)
                    {
                        Debug.LogWarning($"Extremely large hard value reward calculated: {scaledReward} at level {level}. Capping to {MAX_REWARD}.");
                        scaledReward = MAX_REWARD;
                    }

                    return Mathf.Max(1, Mathf.RoundToInt(scaledReward));
                }
            }

            return 1; // Fallback value
        }

        /// <summary>
        /// Calculates level-based scaling for hard values to ensure they remain relevant at higher levels
        /// </summary>
        /// <param name="level">The player's current level</param>
        /// <returns>The scaling multiplier for hard values</returns>
        private float CalculateHardValueLevelScaling(int level)
        {
            // Use a logarithmic scaling to ensure hard values grow with level but not too aggressively
            // This ensures that a level 1 enemy giving 3 XP will give meaningful XP at higher levels
            float levelProgress = (float)(level - StartingLevel) / (MaxLevel - StartingLevel);

            // Scale from 1x at starting level to ~5x at max level using logarithmic curve
            return 1.0f + (Mathf.Log(1.0f + levelProgress * 4.0f) / Mathf.Log(5.0f)) * 4.0f;
        }

        private void CalculateExperienceRequirements()
        {
            // Initialize the list
            experienceRequirements = new List<int>();

            // Set the starting experience
            int baseRequirement = StartingExperience;
            experienceRequirements.Add(baseRequirement);

            // If no algorithm is selected, try to find one in the database
            if (selectedAlgorithm == null)
            {
                AlgorithmDatabase algorithmDB = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
                if (algorithmDB != null && algorithmDB.algorithms != null && algorithmDB.algorithms.Count > 0)
                {
                    // Try to find a SineWave algorithm or use the first one
                    selectedAlgorithm = algorithmDB.algorithms.Find(a => a != null && a.Name != null && a.Name.ToLower().Contains("sine"));
                    if (selectedAlgorithm == null)
                    {
                        selectedAlgorithm = algorithmDB.algorithms[0];
                    }

                    // Update the rewards data with the selected algorithm
                    if (rewardsData != null)
                    {
                        rewardsData.selectedAlgorithm = selectedAlgorithm;
                        EditorUtility.SetDirty(rewardsData);
                    }
                }
            }

            // If we have a selected algorithm, use it
            if (selectedAlgorithm != null)
            {
                // Calculate experience requirements for each level using the algorithm
                int currentRequirement = baseRequirement;

                for (int level = StartingLevel + 1; level <= MaxLevel; level++)
                {
                    try
                    {
                        // Calculate the next requirement using the algorithm
                        int nextRequirement = selectedAlgorithm.CalculateNextRequirement(
                            currentRequirement,
                            level - 1, // Current level (before level up)
                            LevelUpMultiplier,
                            StartingLevel,
                            MaxLevel);

                        // Ensure we have a valid value (greater than previous and positive)
                        if (nextRequirement <= currentRequirement || nextRequirement <= 0)
                        {
                            // If the formula produced an invalid value, use a fallback
                            nextRequirement = Mathf.RoundToInt(currentRequirement * 1.05f);
                            Debug.LogWarning($"Algorithm {selectedAlgorithm.Name} produced invalid XP requirement {nextRequirement} at level {level}. Using fallback: {nextRequirement}");
                        }

                        experienceRequirements.Add(nextRequirement);
                        currentRequirement = nextRequirement;
                    }
                    catch (Exception e)
                    {
                        // If there's an error, use a fallback formula
                        string algorithmName = selectedAlgorithm != null ? selectedAlgorithm.Name : "Unknown";
                        Debug.LogWarning($"Error calculating curve with algorithm {algorithmName}: {e.Message}. Using fallback.");
                        float fallbackMultiplier = LevelUpMultiplier > 0.01f ? LevelUpMultiplier : 1.05f;
                        int nextRequirement = Mathf.RoundToInt(currentRequirement * fallbackMultiplier);
                        experienceRequirements.Add(nextRequirement);
                        currentRequirement = nextRequirement;
                    }
                }
            }
            else
            {
                // If no algorithm is selected, use a simple fallback
                Debug.LogWarning("No algorithm selected. Using simple linear progression.");

                // Calculate a simple linear progression
                int currentRequirement = baseRequirement;
                for (int level = StartingLevel + 1; level <= MaxLevel; level++)
                {
                    float multiplier = 1.1f * LevelUpMultiplier;
                    if (multiplier < 1.01f) multiplier = 1.01f; // Ensure at least 1% increase

                    int nextRequirement = Mathf.RoundToInt(currentRequirement * multiplier);
                    experienceRequirements.Add(nextRequirement);
                    currentRequirement = nextRequirement;
                }
            }

            // Ensure all values are at least increasing
            for (int i = 1; i < experienceRequirements.Count; i++)
            {
                if (experienceRequirements[i] <= experienceRequirements[i - 1])
                {
                    experienceRequirements[i] = experienceRequirements[i - 1] + 1;
                }
            }

            // Run simulation with the updated experience requirements
            RunSimulation();
        }

        private void RunSimulation()
        {
            // Initialize the simulation results list
            simulationResults = new List<SimulationResult>();

            // If no experience requirements, calculate them first
            if (experienceRequirements == null || experienceRequirements.Count == 0)
            {
                CalculateExperienceRequirements();
            }

            // If still no experience requirements, return
            if (experienceRequirements == null || experienceRequirements.Count == 0)
            {
                return;
            }

            // Run the simulation with current settings
            simulationResults = RunSimulationForPlayerType();

            // Mark cached data as dirty when simulation changes
            playerTypeComparisonDirty = true;
            rewardPreviewDirty = true;
        }

        private List<SimulationResult> RunSimulationForPlayerType()
        {
            // Create a new list for the results
            List<SimulationResult> results = new List<SimulationResult>();

            // If no experience requirements, return empty list
            if (experienceRequirements == null || experienceRequirements.Count == 0)
            {
                return results;
            }

            // Calculate actions per day
            float actionsPerDay = playTimePerDay * actionsPerHour;

            // Calculate XP per day based on reward categories and player progression
            float totalXpPerDay = 0;

            // For accurate simulation, we need to use the actual XP requirements
            // without any artificial weighting or scaling

            // Use the starting level's XP requirement as the baseline
            int baselineXp = experienceRequirements[0];

            // For more accurate simulation, we'll use the actual XP requirements
            // at each level rather than a weighted average

            // Calculate average XP per action based on reward categories
            // We'll calculate this dynamically per level to account for XP scaling
            var allCategories = currentRewardType == RewardType.Percentage
                ? rewardCategories.Keys.ToList()
                : hardValueRewardCategories.Keys.ToList();

            foreach (string categoryName in allCategories)
            {
                // Calculate the reward for this category at the starting level as baseline
                int currentLevel = StartingLevel;
                int rewardAmount = CalculateExperienceRewardForCategory(categoryName, baselineXp, currentLevel);

                // Assume equal distribution of actions across categories
                float actionsPerDayForCategory = actionsPerDay / allCategories.Count;

                // Apply the XP multiplier to simulate more efficient gameplay or bonus XP periods
                float adjustedRewardAmount = rewardAmount * xpPerAction;

                // Calculate XP per day for this category (baseline)
                totalXpPerDay += adjustedRewardAmount * actionsPerDayForCategory;
            }

            // If no XP per day, use a default value
            if (totalXpPerDay <= 0)
            {
                Debug.LogWarning("No XP per day calculated. Using default value of 100 XP per day.");
                totalXpPerDay = 100; // Default value to prevent division by zero
            }

            // Calculate time to reach each level
            float totalDays = 0;
            float totalHours = 0;
            DateTime currentDate = DateTime.Now;

            // Add starting level
            results.Add(new SimulationResult
            {
                Level = StartingLevel,
                ExperienceRequired = StartingExperience,
                HoursToLevel = 0,
                DaysToLevel = 0,
                TotalDays = 0,
                TotalHours = 0,
                EstimatedDate = currentDate
            });

            // Calculate for each level
            for (int i = 0; i < experienceRequirements.Count && (StartingLevel + i + 1) <= MaxLevel; i++)
            {
                int level = StartingLevel + i + 1;
                int xpRequired = experienceRequirements[i];

                // Calculate XP per day for this specific level, accounting for XP scaling
                float levelXpPerDay = 0f;

                foreach (string categoryName in allCategories)
                {
                    // Calculate the reward for this category at the current level (with scaling)
                    int rewardAmount = CalculateExperienceRewardForCategory(categoryName, xpRequired, level);

                    // Assume equal distribution of actions across categories
                    float actionsPerDayForCategory = actionsPerDay / allCategories.Count;

                    // Apply the XP multiplier to simulate more efficient gameplay or bonus XP periods
                    float adjustedRewardAmount = rewardAmount * xpPerAction;

                    // Calculate XP per day for this category at this level
                    levelXpPerDay += adjustedRewardAmount * actionsPerDayForCategory;
                }

                // Ensure we have a minimum XP per day to avoid division by zero
                levelXpPerDay = Mathf.Max(0.1f, levelXpPerDay);

                // Calculate days to level with the level-specific XP rate
                float daysToLevel = xpRequired / levelXpPerDay;

                // Cap extremely large values to prevent overflow
                const float MAX_DAYS_PER_LEVEL = 3650000f; // ~10,000 years
                if (daysToLevel > MAX_DAYS_PER_LEVEL)
                {
                    Debug.LogWarning($"Extremely slow progression detected at level {level}. Capping days to level at {MAX_DAYS_PER_LEVEL} days.");
                    daysToLevel = MAX_DAYS_PER_LEVEL;
                }

                float hoursToLevel = daysToLevel * 24;

                // Update totals
                totalDays += daysToLevel;
                totalHours += hoursToLevel;

                // Calculate estimated date with safety check to prevent DateTime overflow
                DateTime estimatedDate;
                try
                {
                    // Prevent DateTime overflow by capping extremely large day values
                    // DateTime can only handle about 10,000 years worth of days
                    const float MAX_SAFE_DAYS = 3650000f; // ~10,000 years
                    float safeDaysToAdd = Mathf.Min(daysToLevel, MAX_SAFE_DAYS);

                    estimatedDate = currentDate.AddDays(safeDaysToAdd);
                    currentDate = estimatedDate;
                }
                catch (ArgumentOutOfRangeException)
                {
                    // If we still get an overflow, use a far future date instead
                    estimatedDate = new DateTime(9999, 12, 31); // Maximum valid DateTime
                    currentDate = estimatedDate;

                    // Add this result to the list
                    results.Add(new SimulationResult
                    {
                        Level = level,
                        ExperienceRequired = xpRequired,
                        HoursToLevel = hoursToLevel,
                        DaysToLevel = daysToLevel,
                        TotalDays = totalDays,
                        TotalHours = totalHours,
                        EstimatedDate = estimatedDate
                    });

                    // Add a note about the date limitation
                    Debug.Log($"Date calculation reached maximum limit at level {level}. Remaining levels will show the maximum date.");

                    // For remaining levels, just use the same maximum date
                    for (int j = i + 1; j < experienceRequirements.Count && (StartingLevel + j + 1) <= MaxLevel; j++)
                    {
                        int nextLevel = StartingLevel + j + 1;
                        int nextXpRequired = experienceRequirements[j];

                        // Calculate time to level using the actual XP requirements
                        // without any artificial scaling for accurate simulation
                        float nextDaysToLevel = nextXpRequired / Mathf.Max(0.1f, totalXpPerDay);
                        float nextHoursToLevel = nextDaysToLevel * 24;

                        // Update totals
                        totalDays += nextDaysToLevel;
                        totalHours += nextHoursToLevel;

                        // Add to results with the maximum date
                        results.Add(new SimulationResult
                        {
                            Level = nextLevel,
                            ExperienceRequired = nextXpRequired,
                            HoursToLevel = nextHoursToLevel,
                            DaysToLevel = nextDaysToLevel,
                            TotalDays = totalDays,
                            TotalHours = totalHours,
                            EstimatedDate = estimatedDate // Same maximum date
                        });
                    }

                    // Break out of the loop since we've handled all levels
                    break;
                }

                // If we didn't hit the catch block, add the result normally
                if (!estimatedDate.Year.Equals(9999))
                {
                    // Add to results (only if we didn't already add in the catch block)
                    results.Add(new SimulationResult
                    {
                        Level = level,
                        ExperienceRequired = xpRequired,
                        HoursToLevel = hoursToLevel,
                        DaysToLevel = daysToLevel,
                        TotalDays = totalDays,
                        TotalHours = totalHours,
                        EstimatedDate = estimatedDate
                    });
                }
            }

            return results;
        }
        #endregion

        #region UI Drawing Methods
        private void DrawAlgorithmSettings()
        {
            // Algorithm & Settings Section
            EditorGUILayout.LabelField("Algorithm & Settings", headerStyle);
            EditorGUILayout.Space(5);

            // Algorithm selection
            EditorGUI.BeginChangeCheck();

            // Load all available algorithms
            AlgorithmDatabase algorithmDB = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");
            if (algorithmDB != null && algorithmDB.algorithms.Count > 0)
            {
                // Create a list of algorithm names for the popup
                List<string> algorithmNames = new List<string>();
                List<LevelingAlgorithmBase> algorithms = new List<LevelingAlgorithmBase>();

                // Add all algorithms from the database
                foreach (var algorithm in algorithmDB.algorithms)
                {
                    algorithmNames.Add(algorithm.Name);
                    algorithms.Add(algorithm);
                }

                // Find the index of the currently selected algorithm
                int selectedIndex = 0;
                if (selectedAlgorithm != null)
                {
                    for (int i = 0; i < algorithms.Count; i++)
                    {
                        if (algorithms[i] != null && algorithms[i].uniqueID == selectedAlgorithm.uniqueID)
                        {
                            selectedIndex = i;
                            break;
                        }
                    }
                }

                // Use the searchable popup from Utilities
                Utilities.SearchablePopup(
                    selectedIndex,
                    "Leveling Algorithm",
                    algorithmNames.ToArray(),
                    (newIndex) => {
                        selectedAlgorithm = algorithms[newIndex];
                        CalculateExperienceRequirements();
                        RunSimulation();
                    }
                );
            }
            else
            {
                // Show a warning if no algorithms are available
                EditorGUILayout.HelpBox("No leveling algorithms found. Please create at least one algorithm in the Algorithm Database.", MessageType.Warning);

                // Add a button to create a default algorithm
                if (GUILayout.Button("Create Default Algorithm Database", GUILayout.Height(30)))
                {
                    // Create a default algorithm database
                    AlgorithmDatabaseManager.CreateDefaultAlgorithmDatabase();

                    // Refresh the asset database
                    AssetDatabase.Refresh();
                }
            }

            // Draw the algorithm description if available
            if (selectedAlgorithm != null)
            {
                EditorGUILayout.Space(5);
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                EditorGUILayout.LabelField(selectedAlgorithm.Name, subHeaderStyle);
                EditorGUILayout.LabelField(selectedAlgorithm.Description, EditorStyles.wordWrappedLabel);
                EditorGUILayout.EndVertical();
            }

            EditorGUILayout.Space(10);

            // Level Up Multiplier (minimum 1.0 to avoid issues with values below 1.0)
            float newLevelUpMultiplier = EditorGUILayout.Slider("Level Up Multiplier", LevelUpMultiplier, 1.0f, 5.0f);
            if (newLevelUpMultiplier != LevelUpMultiplier)
            {
                window.UpdateSharedSettings(levelUpMultiplier: newLevelUpMultiplier);
                CalculateExperienceRequirements();
                RunSimulation();
            }

            // Show a tooltip about the multiplier
            EditorGUILayout.HelpBox("The Level Up Multiplier affects how quickly experience requirements increase between levels. Higher values create steeper curves. Minimum value is 1.0 to ensure proper progression.", MessageType.Info);

            // Starting Experience
            int newStartingExperience = EditorGUILayout.IntField("Starting Experience", StartingExperience);
            if (newStartingExperience < 1) newStartingExperience = 1;
            if (newStartingExperience != StartingExperience)
            {
                window.UpdateSharedSettings(startingExperience: newStartingExperience);
                CalculateExperienceRequirements();
                RunSimulation();
            }

            // Starting Level
            int newStartingLevel = EditorGUILayout.IntField("Starting Level", StartingLevel);
            if (newStartingLevel < 1) newStartingLevel = 1;
            if (newStartingLevel != StartingLevel)
            {
                window.UpdateSharedSettings(startingLevel: newStartingLevel);
                CalculateExperienceRequirements();
                RunSimulation();
            }

            // Max Level
            int newMaxLevel = EditorGUILayout.IntField("Max Level", MaxLevel);
            if (newMaxLevel < StartingLevel + 5) newMaxLevel = StartingLevel + 5;
            if (newMaxLevel != MaxLevel)
            {
                window.UpdateSharedSettings(maxLevel: newMaxLevel);
                CalculateExperienceRequirements();
                RunSimulation();
            }

            // Level range size
            int newLevelRangeSize = EditorGUILayout.IntSlider("Level Range Size", levelRangeSize, 1, 10);
            if (newLevelRangeSize != levelRangeSize)
            {
                levelRangeSize = newLevelRangeSize;
                CalculateExperienceRequirements();
                RunSimulation();
            }

            // XP Scaling Settings (moved here from reward categories section)
            EditorGUILayout.Space(5);
            DrawXpScalingSettings();

            // Show detailed breakdown
            bool newShowDetailedBreakdown = EditorGUILayout.Toggle("Show Detailed Breakdown", showDetailedBreakdown);
            if (newShowDetailedBreakdown != showDetailedBreakdown)
            {
                showDetailedBreakdown = newShowDetailedBreakdown;
                SaveData(); // Save the setting
                window.Repaint();
            }

            // Show level curve preview toggle
            bool newShowLevelCurvePreview = EditorGUILayout.Toggle("Show Level Curve Preview", showLevelCurvePreview);
            if (newShowLevelCurvePreview != showLevelCurvePreview)
            {
                showLevelCurvePreview = newShowLevelCurvePreview;
                SaveData(); // Save the setting
                window.Repaint();
            }

            EditorGUILayout.Space(10);

            // Draw the leveling curve graph (now collapsible)
            if (showLevelCurvePreview && experienceRequirements != null && experienceRequirements.Count > 0)
            {
                // Draw the graph with a fixed height and expanded width
                EditorGUILayout.BeginVertical(EditorStyles.helpBox, GUILayout.ExpandWidth(true));

                // Add a title for the graph
                EditorGUILayout.LabelField("Leveling Curve Preview", subHeaderStyle);
                EditorGUILayout.Space(5);

                // Use the algorithm if available, otherwise fall back to the legacy difficulty
                if (selectedAlgorithm != null)
                {
                    // Calculate the available width for the graph
                    // Make sure we have a valid panel width
                    float graphWidth = Mathf.Max(300, leftPanelWidth - 40);
                    curvePreview.DrawCurvePreview(selectedAlgorithm, LevelUpMultiplier, StartingExperience, StartingLevel, MaxLevel, graphWidth);
                }
                else
                {
                    // If no algorithm is selected, show a message
                    EditorGUILayout.HelpBox("Please select a leveling algorithm to preview the curve.", MessageType.Info);
                }

                EditorGUILayout.EndVertical();
            }

            EditorGUILayout.Space(10);

            // Player Type Settings
            EditorGUILayout.LabelField("Player Type Settings", headerStyle);
            EditorGUILayout.Space(5);

            // Player type selection
            EditorGUILayout.BeginHorizontal();

            foreach (PlayerType type in Enum.GetValues(typeof(PlayerType)))
            {
                // Create a style for the button
                GUIStyle buttonStyle = new GUIStyle(GUI.skin.button);

                // Highlight the selected player type
                if (type == selectedPlayerType)
                {
                    buttonStyle.normal.background = EditorGUIUtility.whiteTexture;
                    buttonStyle.normal.textColor = Color.black;
                    buttonStyle.fontStyle = FontStyle.Bold;
                }

                // Add a button to select this player type
                if (GUILayout.Button(type.ToString(), buttonStyle, GUILayout.Height(25)))
                {
                    // Save current settings to the current player type
                    if (playerTypeSettings.ContainsKey(selectedPlayerType))
                    {
                        playerTypeSettings[selectedPlayerType] = new PlayerTypeSettings(
                            playTimePerDay, actionsPerHour, xpPerAction);
                    }

                    // Switch to new player type
                    selectedPlayerType = type;

                    // Load settings for the new player type
                    UpdatePlayerTypeSettings();

                    // Run simulation with new settings
                    RunSimulation();

                    // Force repaint
                    window.Repaint();
                }
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(5);

            // Player type settings
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            // Simulation mode selection
            SimulationMode newSimulationMode = (SimulationMode)EditorGUILayout.EnumPopup("Simulation Mode", simulationMode);
            if (newSimulationMode != simulationMode)
            {
                simulationMode = newSimulationMode;
                RunSimulation();
            }

            // Help box explaining the simulation mode
            string helpText = simulationMode == SimulationMode.Simple ?
                "Simple mode uses preset player types with predefined activity levels." :
                "Advanced mode allows you to customize all player activity parameters for more precise simulation.";
            EditorGUILayout.HelpBox(helpText, MessageType.Info);

            EditorGUILayout.Space(5);

            // Play time per day
            float newPlayTimePerDay = EditorGUILayout.Slider("Play Time (hours/day)", playTimePerDay, 0.5f, 12f);
            if (newPlayTimePerDay != playTimePerDay)
            {
                playTimePerDay = newPlayTimePerDay;

                // Update the player type settings
                if (playerTypeSettings.ContainsKey(selectedPlayerType))
                {
                    playerTypeSettings[selectedPlayerType] = new PlayerTypeSettings(
                        playTimePerDay, actionsPerHour, xpPerAction);
                }

                // Run simulation with new settings
                RunSimulation();
            }

            // Actions per hour (converted from per minute for better UX)
            float actionsPerMinute = actionsPerHour / 60f;
            float newActionsPerMinute = EditorGUILayout.Slider("XP Actions per Minute", actionsPerMinute, 1f, 60f);
            if (newActionsPerMinute != actionsPerMinute)
            {
                actionsPerHour = newActionsPerMinute * 60f;

                // Update the player type settings
                if (playerTypeSettings.ContainsKey(selectedPlayerType))
                {
                    playerTypeSettings[selectedPlayerType] = new PlayerTypeSettings(
                        playTimePerDay, actionsPerHour, xpPerAction);
                }

                // Run simulation with new settings
                RunSimulation();
            }

            // Show tooltip explaining what XP Actions means
            EditorGUILayout.HelpBox("XP Actions are gameplay activities that award experience points. This represents how many such actions a player performs per minute on average.", MessageType.Info);

            // Only show XP per Action in Advanced mode
            if (simulationMode == SimulationMode.Advanced)
            {
                // XP per action
                float newXpPerAction = EditorGUILayout.Slider("XP Multiplier", xpPerAction, 0.1f, 5f);
                if (newXpPerAction != xpPerAction)
                {
                    xpPerAction = newXpPerAction;

                    // Update the player type settings
                    if (playerTypeSettings.ContainsKey(selectedPlayerType))
                    {
                        playerTypeSettings[selectedPlayerType] = new PlayerTypeSettings(
                            playTimePerDay, actionsPerHour, xpPerAction);
                    }

                    // Run simulation with new settings
                    RunSimulation();
                }

                EditorGUILayout.HelpBox("XP Multiplier affects how much XP is earned per action. Higher values simulate more efficient gameplay or bonus XP periods.", MessageType.Info);
            }

            // Reset to defaults button
            if (GUILayout.Button("Reset to Default Settings", GUILayout.Height(25)))
            {
                // Reset this player type to default settings
                if (playerTypeSettings.ContainsKey(selectedPlayerType))
                {
                    switch (selectedPlayerType)
                    {
                        case PlayerType.Casual:
                            playerTypeSettings[selectedPlayerType] = new PlayerTypeSettings(2.0f, 300.0f, 1.0f);
                            break;
                        case PlayerType.Regular:
                            playerTypeSettings[selectedPlayerType] = new PlayerTypeSettings(4.0f, 600.0f, 1.0f);
                            break;
                        case PlayerType.Hardcore:
                            playerTypeSettings[selectedPlayerType] = new PlayerTypeSettings(6.0f, 900.0f, 1.0f);
                            break;
                    }

                    // Update local variables
                    UpdatePlayerTypeSettings();

                    // Run simulation with new settings
                    RunSimulation();
                }
            }

            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);



            // Save button
            if (GUILayout.Button("Save Settings", GUILayout.Height(30)))
            {
                SaveData();
                Debug.Log("Settings saved successfully.");
            }
        }

        private void DrawXpScalingSettings()
        {
            EditorGUILayout.LabelField("XP Scaling Settings", subHeaderStyle);
            EditorGUILayout.Space(5);

            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            // Enable XP Scaling toggle
            bool newUseXpScaling = EditorGUILayout.Toggle("Enable XP Scaling", useXpScaling);
            if (newUseXpScaling != useXpScaling)
            {
                useXpScaling = newUseXpScaling;
                rewardPreviewDirty = true;
                RunSimulation();
            }

            if (useXpScaling)
            {
                EditorGUILayout.HelpBox("XP Scaling simulates how rewards increase as players progress to higher level areas, fight stronger enemies, and complete harder content.", MessageType.Info);

                // XP Scaling Mode using searchable enum dropdown
                Utilities.SearchableEnumPopup(
                    xpScalingMode,
                    "Scaling Mode",
                    newXpScalingMode => {
                        if (newXpScalingMode != xpScalingMode)
                        {
                            xpScalingMode = newXpScalingMode;
                            rewardPreviewDirty = true;
                            RunSimulation();
                        }
                    }
                );

                // XP Scaling Factor
                float newXpScalingFactor = EditorGUILayout.Slider("Scaling Factor", xpScalingFactor, 1.0f, 3.0f);
                if (newXpScalingFactor != xpScalingFactor)
                {
                    xpScalingFactor = newXpScalingFactor;
                    rewardPreviewDirty = true;
                    RunSimulation();
                }

                // Help text for scaling modes
                string scalingHelpText = xpScalingMode switch
                {
                    XpScalingMode.Linear => "Linear: XP rewards increase steadily with level progression.",
                    XpScalingMode.Exponential => "Exponential: XP rewards increase rapidly at higher levels.",
                    XpScalingMode.Logarithmic => "Logarithmic: XP rewards increase quickly early, then level off.",
                    _ => ""
                };

                if (!string.IsNullOrEmpty(scalingHelpText))
                {
                    EditorGUILayout.HelpBox(scalingHelpText, MessageType.Info);
                }
            }
            else
            {
                EditorGUILayout.HelpBox("XP Scaling is disabled. Rewards will be a fixed percentage of level requirements.", MessageType.Info);
            }

            EditorGUILayout.EndVertical();
        }

        private void DrawRewardCategories()
        {
            // Reward Categories Section
            EditorGUILayout.LabelField("Reward Categories", headerStyle);
            EditorGUILayout.Space(5);

            // Reward Type Toggle
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("Reward Type", categoryHeaderStyle);

            EditorGUI.BeginChangeCheck();
            RewardType newRewardType = (RewardType)EditorGUILayout.EnumPopup("Mode", currentRewardType);
            if (EditorGUI.EndChangeCheck())
            {
                currentRewardType = newRewardType;

                // Clear existing categories and reinitialize with new type
                InitializeDefaultRewardCategories();

                // Update the ScriptableObject
                UpdateRewardCategoriesInScriptableObject();

                // Mark reward preview as dirty and run simulation
                rewardPreviewDirty = true;
                RunSimulation();
            }

            // Add help text explaining the difference
            if (currentRewardType == RewardType.Percentage)
            {
                EditorGUILayout.HelpBox(
                    "Percentage Mode: Rewards are calculated as a percentage of the XP required for each level. " +
                    "This ensures rewards scale automatically with level difficulty but can feel inconsistent.",
                    MessageType.Info);
            }
            else
            {
                EditorGUILayout.HelpBox(
                    "Hard Value Mode: Rewards are fixed XP amounts that scale with player level. " +
                    "This provides consistent, predictable rewards that grow meaningfully as players progress.",
                    MessageType.Info);
            }

            EditorGUILayout.EndVertical();
            EditorGUILayout.Space(10);

            // Reward Scaling Information
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("Reward Scaling", categoryHeaderStyle);

            if (useXpScaling)
            {
                EditorGUILayout.HelpBox($"XP Scaling is enabled ({xpScalingMode} mode, {xpScalingFactor:F1}x factor). Rewards increase as players progress to simulate stronger enemies and harder content.", MessageType.Info);

                // Show scaling examples
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField($"Level {StartingLevel} scaling: 1.0x", EditorStyles.miniLabel);
                if (MaxLevel > StartingLevel)
                {
                    int midLevel = StartingLevel + (MaxLevel - StartingLevel) / 2;
                    float midScaling = CalculateXpScalingMultiplier(midLevel);
                    EditorGUILayout.LabelField($"Level {midLevel} scaling: {midScaling:F1}x", EditorStyles.miniLabel);

                    float maxScaling = CalculateXpScalingMultiplier(MaxLevel);
                    EditorGUILayout.LabelField($"Level {MaxLevel} scaling: {maxScaling:F1}x", EditorStyles.miniLabel);
                }
                EditorGUILayout.EndHorizontal();
            }
            else
            {
                EditorGUILayout.HelpBox("XP Scaling is disabled. Rewards are a fixed percentage of level requirements.", MessageType.Info);
            }

            if (selectedAlgorithm != null)
            {
                EditorGUILayout.LabelField($"Using algorithm: {selectedAlgorithm.Name}", EditorStyles.boldLabel);
                EditorGUILayout.LabelField($"Level Up Multiplier: {LevelUpMultiplier:F2}x", EditorStyles.boldLabel);
                if (useXpScaling)
                {
                    EditorGUILayout.LabelField($"XP Scaling: {xpScalingMode} ({xpScalingFactor:F1}x)", EditorStyles.boldLabel);
                }
            }
            else
            {
                EditorGUILayout.HelpBox("No algorithm selected. Please select an algorithm to see reward calculations.", MessageType.Warning);
            }
            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // Display each reward category with a slider
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            if (currentRewardType == RewardType.Percentage)
            {
                EditorGUILayout.LabelField("Reward Categories (% of Level XP)", categoryHeaderStyle);

                // Sort categories by multiplier (descending)
                var sortedCategories = rewardCategories.OrderByDescending(x => x.Value).ToList();
                DrawPercentageRewardCategories(sortedCategories);
            }
            else
            {
                EditorGUILayout.LabelField("Reward Categories (Base XP Values)", categoryHeaderStyle);

                // Sort categories by hard value (descending)
                var sortedCategories = hardValueRewardCategories.OrderByDescending(x => x.Value).ToList();
                DrawHardValueRewardCategories(sortedCategories);
            }
        }

        private void DrawPercentageRewardCategories(List<KeyValuePair<string, float>> sortedCategories)
        {
            foreach (var category in sortedCategories)
            {
                EditorGUILayout.BeginHorizontal();

                // Allow renaming the category
                string newCategory = EditorGUILayout.TextField(category.Key, GUILayout.Width(120));

                // If the category name changed, update the dictionary
                if (newCategory != category.Key && !rewardCategories.ContainsKey(newCategory))
                {
                    float value = rewardCategories[category.Key];
                    rewardCategories.Remove(category.Key);
                    rewardCategories.Add(newCategory, value);

                    // Update the ScriptableObject
                    UpdateRewardCategoriesInScriptableObject();

                    // Run simulation with new settings
                    RunSimulation();

                    // Force repaint
                    window.Repaint();

                    // Exit GUI to prevent errors
                    GUIUtility.ExitGUI();
                }
                else
                {
                    // Update the multiplier value with finer control (0.001 to 1.0)
                    float multiplier = rewardCategories[category.Key];
                    float percentValue = multiplier * 100f; // Convert to percentage for display

                    EditorGUI.BeginChangeCheck();

                    // Display current value with 3 decimal places
                    string percentLabel = $"{percentValue:F3}";
                    EditorGUILayout.LabelField(percentLabel, GUILayout.Width(50));

                    // Create a slider for the full range (0.001% - 100%)
                    float newPercentValue = EditorGUILayout.Slider(percentValue, 0.001f, 100f, GUILayout.ExpandWidth(true));
                    if (EditorGUI.EndChangeCheck())
                    {
                        percentValue = newPercentValue;
                        multiplier = percentValue / 100f;
                        rewardCategories[category.Key] = multiplier;

                        // Update the ScriptableObject
                        UpdateRewardCategoriesInScriptableObject();

                        // Mark reward preview as dirty
                        rewardPreviewDirty = true;

                        // Run simulation with new settings
                        RunSimulation();
                    }
                }

                // Display the % symbol
                GUILayout.Label("%", GUILayout.Width(15));

                // Delete button
                if (GUILayout.Button("X", GUILayout.Width(20)))
                {
                    rewardCategories.Remove(category.Key);

                    // Update the ScriptableObject
                    UpdateRewardCategoriesInScriptableObject();

                    // Run simulation with new settings
                    RunSimulation();

                    // Force repaint
                    window.Repaint();

                    // Exit GUI to prevent errors
                    GUIUtility.ExitGUI();
                }

                EditorGUILayout.EndHorizontal();
            }

            // Add buttons for managing categories
            EditorGUILayout.Space(5);
            EditorGUILayout.BeginHorizontal();

            // Add new category button
            if (GUILayout.Button("Add New Category", GUILayout.Height(25)))
            {
                string newCategoryName = "New Category";
                int suffix = 0;

                // Ensure unique name
                while (rewardCategories.ContainsKey(newCategoryName + (suffix == 0 ? "" : suffix.ToString())))
                {
                    suffix++;
                }

                rewardCategories.Add(newCategoryName + (suffix == 0 ? "" : suffix.ToString()), 0.25f);

                // Update the ScriptableObject
                UpdateRewardCategoriesInScriptableObject();

                // Run simulation with new settings
                RunSimulation();
            }

            // Reset to Default button
            if (GUILayout.Button("Reset to Default", GUILayout.Height(25)))
            {
                // Show a confirmation dialog
                if (EditorUtility.DisplayDialog("Reset Reward Categories",
                    "Are you sure you want to reset all reward categories to their default values?",
                    "Yes, Reset", "Cancel"))
                {
                    // Initialize with default reward categories
                    InitializeDefaultRewardCategories();

                    // Run simulation with new settings
                    RunSimulation();

                    // Force repaint
                    window.Repaint();
                }
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // Preview of actions needed to reach different levels
            if (experienceRequirements != null && experienceRequirements.Count > 0)
            {
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                EditorGUILayout.LabelField("Actions Needed to Reach Level", categoryHeaderStyle);

                // Add help box explaining what the values mean
                EditorGUILayout.HelpBox(
                    "This table shows how many actions of each type are needed to reach specific levels. " +
                    "For example, if 'Epic Quest' shows '10' under 'L5', it means you need to complete 10 Epic Quests to reach level 5. " +
                    "Lower numbers indicate more efficient leveling activities.",
                    MessageType.Info);

                // Calculate more representative preview levels based on progression curve
                List<int> previewLevelsList = new List<int>();

                // Always include starting level
                previewLevelsList.Add(StartingLevel);

                // Calculate intermediate levels based on the max level
                int levelRange = MaxLevel - StartingLevel;

                if (levelRange <= 10)
                {
                    // For small level ranges, show all levels
                    for (int i = StartingLevel + 1; i <= MaxLevel; i++)
                    {
                        previewLevelsList.Add(i);
                    }
                }
                else if (levelRange <= 25)
                {
                    // For medium level ranges, show every 5 levels
                    for (int i = StartingLevel + 5; i < MaxLevel; i += 5)
                    {
                        previewLevelsList.Add(i);
                    }
                    // Always include max level
                    previewLevelsList.Add(MaxLevel);
                }
                else if (levelRange <= 50)
                {
                    // For larger level ranges, show key progression points
                    previewLevelsList.Add(StartingLevel + 5);
                    previewLevelsList.Add(StartingLevel + 10);
                    previewLevelsList.Add(StartingLevel + 20);
                    previewLevelsList.Add(Mathf.RoundToInt(StartingLevel + levelRange * 0.5f)); // 50% point
                    previewLevelsList.Add(MaxLevel);
                }
                else
                {
                    // For very large level ranges, show distributed points
                    previewLevelsList.Add(StartingLevel + 10);
                    previewLevelsList.Add(StartingLevel + 25);
                    previewLevelsList.Add(Mathf.RoundToInt(StartingLevel + levelRange * 0.25f)); // 25% point
                    previewLevelsList.Add(Mathf.RoundToInt(StartingLevel + levelRange * 0.5f));  // 50% point
                    previewLevelsList.Add(Mathf.RoundToInt(StartingLevel + levelRange * 0.75f)); // 75% point
                    previewLevelsList.Add(MaxLevel);
                }

                // Remove duplicates and sort
                previewLevelsList = previewLevelsList.Distinct().OrderBy(l => l).ToList();

                // Convert to array for use in the UI
                int[] previewLevels = previewLevelsList.ToArray();

                // Calculate available width for the table
                float availableTableWidth = leftPanelWidth - 40; // Account for padding and scrollbar

                // Limit the number of preview levels to fit the available width
                int maxPreviewLevels = Mathf.Max(2, Mathf.FloorToInt((availableTableWidth - 100) / 70)); // 100 for category name, 70 per level column
                int[] limitedPreviewLevels = previewLevels.Take(maxPreviewLevels).ToArray();

                // Table header
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Category", GUILayout.Width(100));

                foreach (int level in limitedPreviewLevels)
                {
                    if (level <= MaxLevel)
                    {
                        string headerText = useXpScaling ? $"L{level}" : $"L{level}";
                        EditorGUILayout.LabelField(headerText, GUILayout.Width(65));
                    }
                }

                EditorGUILayout.EndHorizontal();

                // Draw a separator line
                Rect separatorRect = EditorGUILayout.GetControlRect(false, 1);
                EditorGUI.DrawRect(separatorRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));

                // Update cached reward preview if dirty
                if (rewardPreviewDirty)
                {
                    UpdateRewardPreviewCache(previewLevels);
                    rewardPreviewDirty = false;
                }

                // Display each category with rewards at different levels
                foreach (var category in sortedCategories)
                {
                    EditorGUILayout.BeginHorizontal();

                    // Category name (truncated if too long)
                    string categoryName = category.Key.Length > 12 ? category.Key.Substring(0, 12) + "..." : category.Key;
                    EditorGUILayout.LabelField(categoryName, GUILayout.Width(100));

                    // Actions needed at each preview level (from cache)
                    foreach (int level in limitedPreviewLevels)
                    {
                        if (level <= MaxLevel)
                        {
                            // Get cached actions needed value
                            if (cachedRewardPreview.TryGetValue(category.Key, out var levelActionsNeeded) &&
                                levelActionsNeeded.TryGetValue(level, out int actionsNeeded))
                            {
                                // Display the actions needed with compact formatting
                                string actionsText = FormatCompactNumber(actionsNeeded);
                                EditorGUILayout.LabelField(actionsText, GUILayout.Width(65));
                            }
                            else
                            {
                                EditorGUILayout.LabelField("-", GUILayout.Width(65));
                            }
                        }
                    }

                    EditorGUILayout.EndHorizontal();
                }

                EditorGUILayout.EndVertical();
            }
        }

        private void DrawHardValueRewardCategories(List<KeyValuePair<string, int>> sortedCategories)
        {
            foreach (var category in sortedCategories)
            {
                EditorGUILayout.BeginHorizontal();

                // Allow renaming the category
                string newCategory = EditorGUILayout.TextField(category.Key, GUILayout.Width(120));

                // If the category name changed, update the dictionary
                if (newCategory != category.Key && !hardValueRewardCategories.ContainsKey(newCategory))
                {
                    int value = hardValueRewardCategories[category.Key];
                    hardValueRewardCategories.Remove(category.Key);
                    hardValueRewardCategories.Add(newCategory, value);

                    // Update the ScriptableObject
                    UpdateRewardCategoriesInScriptableObject();

                    // Run simulation with new settings
                    RunSimulation();

                    // Force repaint
                    window.Repaint();

                    // Exit GUI to prevent errors
                    GUIUtility.ExitGUI();
                }
                else
                {
                    // Update the hard value with control (1 to 10000)
                    int hardValue = hardValueRewardCategories[category.Key];

                    EditorGUI.BeginChangeCheck();

                    // Display current value
                    string valueLabel = hardValue.ToString();
                    EditorGUILayout.LabelField(valueLabel, GUILayout.Width(50));

                    // Create a slider for the range (1 - 10000)
                    int newHardValue = EditorGUILayout.IntSlider(hardValue, 1, 10000, GUILayout.ExpandWidth(true));
                    if (EditorGUI.EndChangeCheck())
                    {
                        hardValueRewardCategories[category.Key] = newHardValue;

                        // Update the ScriptableObject
                        UpdateRewardCategoriesInScriptableObject();

                        // Mark reward preview as dirty
                        rewardPreviewDirty = true;

                        // Run simulation with new settings
                        RunSimulation();
                    }
                }

                // Display the XP symbol
                GUILayout.Label("XP", GUILayout.Width(25));

                // Delete button
                if (GUILayout.Button("X", GUILayout.Width(20)))
                {
                    hardValueRewardCategories.Remove(category.Key);

                    // Update the ScriptableObject
                    UpdateRewardCategoriesInScriptableObject();

                    // Run simulation with new settings
                    RunSimulation();

                    // Force repaint
                    window.Repaint();

                    // Exit GUI to prevent errors
                    GUIUtility.ExitGUI();
                }

                EditorGUILayout.EndHorizontal();
            }

            // Add buttons for managing categories
            EditorGUILayout.Space(5);
            EditorGUILayout.BeginHorizontal();

            // Add new category button
            if (GUILayout.Button("Add New Category", GUILayout.Height(25)))
            {
                string newCategoryName = "New Category";
                int suffix = 0;

                // Ensure unique name
                while (hardValueRewardCategories.ContainsKey(newCategoryName + (suffix == 0 ? "" : suffix.ToString())))
                {
                    suffix++;
                }

                hardValueRewardCategories.Add(newCategoryName + (suffix == 0 ? "" : suffix.ToString()), 50);

                // Update the ScriptableObject
                UpdateRewardCategoriesInScriptableObject();

                // Run simulation with new settings
                RunSimulation();
            }

            // Reset to Default button
            if (GUILayout.Button("Reset to Default", GUILayout.Height(25)))
            {
                // Show a confirmation dialog
                if (EditorUtility.DisplayDialog("Reset Reward Categories",
                    "Are you sure you want to reset all reward categories to their default values?",
                    "Yes, Reset", "Cancel"))
                {
                    // Initialize with default reward categories
                    InitializeDefaultRewardCategories();

                    // Run simulation with new settings
                    RunSimulation();

                    // Force repaint
                    window.Repaint();
                }
            }

            EditorGUILayout.EndHorizontal();

            EditorGUILayout.EndVertical();

            EditorGUILayout.Space(10);

            // Preview of actions needed to reach different levels
            if (experienceRequirements != null && experienceRequirements.Count > 0)
            {
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                EditorGUILayout.LabelField("Actions Needed to Reach Level", categoryHeaderStyle);

                // Add help box explaining what the values mean
                EditorGUILayout.HelpBox(
                    "This table shows how many actions of each type are needed to reach specific levels. " +
                    "For example, if 'Epic Quest' shows '10' under 'L5', it means you need to complete 10 Epic Quests to reach level 5. " +
                    "Lower numbers indicate more efficient leveling activities. Values scale with player level.",
                    MessageType.Info);

                // Calculate more representative preview levels based on progression curve
                List<int> previewLevelsList = new List<int>();

                // Always include starting level
                previewLevelsList.Add(StartingLevel);

                // Calculate intermediate levels based on the max level
                int levelRange = MaxLevel - StartingLevel;

                if (levelRange <= 10)
                {
                    // For small level ranges, show all levels
                    for (int i = StartingLevel + 1; i <= MaxLevel; i++)
                    {
                        previewLevelsList.Add(i);
                    }
                }
                else if (levelRange <= 25)
                {
                    // For medium level ranges, show every 5 levels
                    for (int i = StartingLevel + 5; i < MaxLevel; i += 5)
                    {
                        previewLevelsList.Add(i);
                    }
                    // Always include max level
                    previewLevelsList.Add(MaxLevel);
                }
                else if (levelRange <= 50)
                {
                    // For larger level ranges, show key progression points
                    previewLevelsList.Add(StartingLevel + 5);
                    previewLevelsList.Add(StartingLevel + 10);
                    previewLevelsList.Add(StartingLevel + 20);
                    previewLevelsList.Add(Mathf.RoundToInt(StartingLevel + levelRange * 0.5f)); // 50% point
                    previewLevelsList.Add(MaxLevel);
                }
                else
                {
                    // For very large level ranges, show distributed points
                    previewLevelsList.Add(StartingLevel + 10);
                    previewLevelsList.Add(StartingLevel + 25);
                    previewLevelsList.Add(Mathf.RoundToInt(StartingLevel + levelRange * 0.25f)); // 25% point
                    previewLevelsList.Add(Mathf.RoundToInt(StartingLevel + levelRange * 0.5f));  // 50% point
                    previewLevelsList.Add(Mathf.RoundToInt(StartingLevel + levelRange * 0.75f)); // 75% point
                    previewLevelsList.Add(MaxLevel);
                }

                // Remove duplicates and sort
                previewLevelsList = previewLevelsList.Distinct().OrderBy(l => l).ToList();

                // Convert to array for use in the UI
                int[] previewLevels = previewLevelsList.ToArray();

                // Calculate available width for the table
                float availableTableWidth = leftPanelWidth - 40; // Account for padding and scrollbar

                // Limit the number of preview levels to fit the available width
                int maxPreviewLevels = Mathf.Max(2, Mathf.FloorToInt((availableTableWidth - 100) / 70)); // 100 for category name, 70 per level column
                int[] limitedPreviewLevels = previewLevels.Take(maxPreviewLevels).ToArray();

                // Table header
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Category", GUILayout.Width(100));

                foreach (int level in limitedPreviewLevels)
                {
                    if (level <= MaxLevel)
                    {
                        string headerText = useXpScaling ? $"L{level}" : $"L{level}";
                        EditorGUILayout.LabelField(headerText, GUILayout.Width(65));
                    }
                }

                EditorGUILayout.EndHorizontal();

                // Draw a separator line
                Rect separatorRect = EditorGUILayout.GetControlRect(false, 1);
                EditorGUI.DrawRect(separatorRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));

                // Update cached reward preview if dirty
                if (rewardPreviewDirty)
                {
                    UpdateRewardPreviewCache(previewLevels);
                    rewardPreviewDirty = false;
                }

                // Display each category with rewards at different levels
                foreach (var category in sortedCategories)
                {
                    EditorGUILayout.BeginHorizontal();

                    // Category name (truncated if too long)
                    string categoryName = category.Key.Length > 12 ? category.Key.Substring(0, 12) + "..." : category.Key;
                    EditorGUILayout.LabelField(categoryName, GUILayout.Width(100));

                    // Actions needed at each preview level (from cache)
                    foreach (int level in limitedPreviewLevels)
                    {
                        if (level <= MaxLevel)
                        {
                            // Get cached actions needed value
                            if (cachedRewardPreview.TryGetValue(category.Key, out var levelActionsNeeded) &&
                                levelActionsNeeded.TryGetValue(level, out int actionsNeeded))
                            {
                                // Display the actions needed with compact formatting
                                string actionsText = FormatCompactNumber(actionsNeeded);
                                EditorGUILayout.LabelField(actionsText, GUILayout.Width(65));
                            }
                            else
                            {
                                EditorGUILayout.LabelField("-", GUILayout.Width(65));
                            }
                        }
                    }

                    EditorGUILayout.EndHorizontal();
                }

                EditorGUILayout.EndVertical();
            }
        }

        /// <summary>
        /// Collapses all level range foldouts
        /// </summary>
        private void CollapseAllLevelRanges()
        {
            foreach (int startLevel in levelRangeFoldoutStates.Keys.ToList())
            {
                levelRangeFoldoutStates[startLevel] = false;
            }
            // Force repaint to update the UI immediately
            window.Repaint();
        }

        /// <summary>
        /// Expands all level range foldouts
        /// </summary>
        private void ExpandAllLevelRanges()
        {
            foreach (int startLevel in levelRangeFoldoutStates.Keys.ToList())
            {
                levelRangeFoldoutStates[startLevel] = true;
            }
            // Force repaint to update the UI immediately
            window.Repaint();
        }

        private void DrawRightPanel()
        {
            // Use cached styles instead of creating new ones every frame
            InitializeStyles();

            // Begin scrollview for the entire right panel with dark background
            EditorGUILayout.BeginVertical(darkBackgroundStyle, GUILayout.ExpandWidth(true), GUILayout.ExpandHeight(true));
            rightScrollPosition = EditorGUILayout.BeginScrollView(
                rightScrollPosition,
                false, // No horizontal scrollbar
                true,  // Vertical scrollbar
                GUILayout.ExpandWidth(true),
                GUILayout.ExpandHeight(true));

            // Add a heading for the level progression section
            Rect levelProgressionHeaderRect = EditorGUILayout.GetControlRect(false, 30);
            EditorGUI.DrawRect(levelProgressionHeaderRect, new Color(0.3f, 0.3f, 0.4f));

            // Display the level progression heading
            EditorGUI.LabelField(levelProgressionHeaderRect, "Level Progression", levelProgressionHeaderStyle);

            EditorGUILayout.Space(10);

            // Add Collapse All / Expand All buttons
            EditorGUILayout.BeginHorizontal();
            GUILayout.FlexibleSpace(); // Center buttons

            // Collapse All button
            if (GUILayout.Button("Collapse All", compactButtonStyle, GUILayout.Width(80), GUILayout.Height(20)))
            {
                CollapseAllLevelRanges();
            }

            GUILayout.Space(10);

            // Expand All button
            if (GUILayout.Button("Expand All", compactButtonStyle, GUILayout.Width(80), GUILayout.Height(20)))
            {
                ExpandAllLevelRanges();
            }

            GUILayout.FlexibleSpace(); // Center buttons
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(8);

            // Add a separator line
            Rect mainSeparatorRect = EditorGUILayout.GetControlRect(false, 2);
            EditorGUI.DrawRect(mainSeparatorRect, new Color(0.3f, 0.3f, 0.3f, 0.8f));

            EditorGUILayout.Space(10);

            // Check if we have experience requirements calculated
            if (experienceRequirements != null && experienceRequirements.Count > 0)
            {
                // Use the level range size from the slider
                int rangeSize = levelRangeSize; // Use the value from the slider

                // Calculate how many ranges we need to display all levels up to MaxLevel
                int totalRangesNeeded = Mathf.CeilToInt((float)(MaxLevel - StartingLevel + 1) / rangeSize);

                // Calculate average experience for the first level range
                int totalExp = 0;
                int count = 0;
                int startIdx = Mathf.Max(0, StartingLevel - 1);
                int endIdx = Mathf.Min(experienceRequirements.Count - 1, startIdx + rangeSize - 1);

                for (int i = startIdx; i <= endIdx; i++)
                {
                    if (i < experienceRequirements.Count)
                    {
                        totalExp += experienceRequirements[i];
                        count++;
                    }
                }

                int averageExp = (count > 0) ? totalExp / count : 0;

                // Display first level range
                DisplayLevelRangeWithRewards(StartingLevel, rangeSize, averageExp, rightPanelHeaderStyle);

                // Display additional level ranges
                for (int rangeIndex = 1; rangeIndex < totalRangesNeeded; rangeIndex++)
                {
                    int rangeStartLevel = StartingLevel + (rangeIndex * rangeSize);

                    // Calculate average experience for this level range
                    int rangeTotalExp = 0;
                    int rangeCount = 0;
                    int rangeStartIdx = Mathf.Max(0, rangeStartLevel - 1);
                    int rangeEndIdx = Mathf.Min(experienceRequirements.Count - 1, rangeStartIdx + rangeSize - 1);

                    for (int i = rangeStartIdx; i <= rangeEndIdx; i++)
                    {
                        if (i < experienceRequirements.Count)
                        {
                            rangeTotalExp += experienceRequirements[i];
                            rangeCount++;
                        }
                    }

                    int rangeAverageExp = (rangeCount > 0) ? rangeTotalExp / rangeCount : 0;

                    // Add some space between ranges
                    EditorGUILayout.Space(15);

                    // Display this level range
                    DisplayLevelRangeWithRewards(rangeStartLevel, rangeSize, rangeAverageExp, rightPanelHeaderStyle);
                }
            }
            else
            {
                EditorGUILayout.HelpBox("No experience requirements calculated. Please check your settings.", MessageType.Warning);
            }

            // No additional content needed here as all the information is now displayed in the level range sections

            EditorGUILayout.Space(10);

            // Display simulation results
            if (simulationResults != null && simulationResults.Count > 0)
            {
                // Total needed section
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                EditorGUILayout.LabelField("Progression Summary", EditorStyles.boldLabel);

                // Table header with more descriptive labels
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Level", GUILayout.Width(50));
                EditorGUILayout.LabelField("XP Required", GUILayout.Width(80));
                EditorGUILayout.LabelField("Time for Level", GUILayout.Width(100));
                EditorGUILayout.LabelField("Cumulative Time", GUILayout.Width(100));
                EditorGUILayout.LabelField("Est. Completion", GUILayout.Width(100));
                EditorGUILayout.EndHorizontal();

                // Draw a separator line
                Rect separatorRect = EditorGUILayout.GetControlRect(false, 1);
                EditorGUI.DrawRect(separatorRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));

                // Create a list of levels to display (key milestones)
                List<SimulationResult> milestonesToShow = new List<SimulationResult>();

                // Always add starting level
                milestonesToShow.Add(simulationResults[0]);

                // Use the level range size for milestone frequency
                int milestoneFrequency = levelRangeSize;

                // For very large level ranges, adjust the milestone frequency
                if (MaxLevel > 100 && levelRangeSize < 10)
                    milestoneFrequency = 20;
                else if (MaxLevel > 50 && levelRangeSize < 5)
                    milestoneFrequency = 10;

                // Add milestone levels and max level
                for (int i = 1; i < simulationResults.Count; i++)
                {
                    var result = simulationResults[i];

                    // Skip levels beyond maxLevel
                    if (result.Level > MaxLevel)
                        break;

                    // Only add milestones and the max level
                    if (result.Level % milestoneFrequency == 0 || result.Level == MaxLevel)
                    {
                        milestonesToShow.Add(result);
                    }
                }

                // Display each milestone
                foreach (var result in milestonesToShow)
                {
                    EditorGUILayout.BeginHorizontal();

                    // Level
                    EditorGUILayout.LabelField(result.Level.ToString(), GUILayout.Width(50));

                    // XP Required
                    EditorGUILayout.LabelField(result.ExperienceRequired.ToString("N0"), GUILayout.Width(80));

                    // Time to Level
                    string timeToLevel;
                    if (result.Level == StartingLevel)
                        timeToLevel = "-";
                    else if (result.DaysToLevel < 1)
                        timeToLevel = $"{result.HoursToLevel:F1} hours";
                    else
                        timeToLevel = $"{result.DaysToLevel:F1} days";

                    EditorGUILayout.LabelField(timeToLevel, GUILayout.Width(100));

                    // Total Time
                    string totalTime;
                    if (result.Level == StartingLevel)
                        totalTime = "-";
                    else if (result.TotalDays < 1)
                        totalTime = $"{result.TotalHours:F1} hours";
                    else
                        totalTime = $"{result.TotalDays:F1} days";

                    EditorGUILayout.LabelField(totalTime, GUILayout.Width(100));

                    // Estimated Date
                    string estimatedDate;
                    if (result.Level == StartingLevel)
                        estimatedDate = "Now";
                    else if (result.EstimatedDate.Year >= 9999)
                        estimatedDate = "Far Future";
                    else
                        estimatedDate = result.EstimatedDate.ToString("MMM d, yyyy");
                    EditorGUILayout.LabelField(estimatedDate, GUILayout.Width(100));

                    EditorGUILayout.EndHorizontal();
                }

                EditorGUILayout.EndVertical();

                EditorGUILayout.Space(10);

                // Display player activity summary
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                EditorGUILayout.LabelField("Player Activity Summary", categoryHeaderStyle);

                // Display player type and settings
                EditorGUILayout.LabelField($"Player Type: {selectedPlayerType}", EditorStyles.boldLabel);
                EditorGUILayout.LabelField($"Play Time: {playTimePerDay:F1} hours/day", EditorStyles.label);
                EditorGUILayout.LabelField($"Actions: {actionsPerHour / 60f:F1} per minute", EditorStyles.label);

                // Calculate total actions per day
                float actionsPerDay = playTimePerDay * actionsPerHour;
                EditorGUILayout.LabelField($"Total Actions: {actionsPerDay:F0} per day", EditorStyles.boldLabel);

                // Show XP multiplier if in advanced mode
                if (simulationMode == SimulationMode.Advanced)
                {
                    EditorGUILayout.LabelField($"XP Multiplier: {xpPerAction:F1}x", EditorStyles.boldLabel);
                }

                // Add Player Type Comparison Timeline
                EditorGUILayout.Space(10);
                EditorGUILayout.LabelField("Player Type Comparison", categoryHeaderStyle);

                // Calculate time to max level for each player type (cached)
                if (playerTypeComparisonDirty)
                {
                    UpdatePlayerTypeComparison();
                    playerTypeComparisonDirty = false;
                }

                // Draw the timeline bars
                float maxDays = 0;
                foreach (var days in cachedPlayerTypeDays.Values)
                {
                    if (days > maxDays) maxDays = days;
                }

                if (maxDays > 0)
                {
                    // Draw each player type bar
                    foreach (PlayerType type in Enum.GetValues(typeof(PlayerType)))
                    {
                        if (cachedPlayerTypeDays.TryGetValue(type, out float days))
                        {
                            // Skip if no data
                            if (days <= 0) continue;

                            EditorGUILayout.BeginHorizontal();

                            // Player type label (fixed width)
                            EditorGUILayout.LabelField(type.ToString(), GUILayout.Width(70));

                            // Bar background
                            Rect barRect = EditorGUILayout.GetControlRect(false, 20, GUILayout.ExpandWidth(true));

                            // Calculate bar width as percentage of max days
                            float barWidth = (days / maxDays) * barRect.width;

                            // Draw the bar with color based on player type
                            Color barColor;
                            switch (type)
                            {
                                case PlayerType.Casual:
                                    barColor = new Color(0.2f, 0.6f, 1.0f); // Blue
                                    break;
                                case PlayerType.Regular:
                                    barColor = new Color(0.2f, 0.8f, 0.2f); // Green
                                    break;
                                case PlayerType.Hardcore:
                                    barColor = new Color(1.0f, 0.6f, 0.2f); // Orange
                                    break;
                                default:
                                    barColor = Color.gray;
                                    break;
                            }

                            // Draw the bar
                            Rect filledBarRect = new Rect(barRect.x, barRect.y, barWidth, barRect.height);
                            EditorGUI.DrawRect(filledBarRect, barColor);

                            // Draw the time text on the bar
                            string timeText;
                            if (days < 1)
                                timeText = $"{days * 24:F1} hours";
                            else if (days < 30)
                                timeText = $"{days:F1} days";
                            else if (days < 365)
                                timeText = $"{days / 30:F1} months";
                            else
                                timeText = $"{days / 365:0.0} years"; // Force one decimal place even for whole numbers

                            // Draw the text using cached style
                            Rect textRect = new Rect(filledBarRect.x, filledBarRect.y, filledBarRect.width - 5, filledBarRect.height);
                            EditorGUI.LabelField(textRect, timeText, barTextStyle);

                            EditorGUILayout.EndHorizontal();
                        }
                    }

                    // Add a legend
                    EditorGUILayout.Space(5);
                    EditorGUILayout.BeginHorizontal();
                    GUILayout.FlexibleSpace(); // Center the legend

                    foreach (PlayerType type in Enum.GetValues(typeof(PlayerType)))
                    {
                        // Skip if no data
                        if (!cachedPlayerTypeDays.ContainsKey(type) || cachedPlayerTypeDays[type] <= 0) continue;

                        // Get color for this player type
                        Color legendColor;
                        switch (type)
                        {
                            case PlayerType.Casual:
                                legendColor = new Color(0.2f, 0.6f, 1.0f); // Blue
                                break;
                            case PlayerType.Regular:
                                legendColor = new Color(0.2f, 0.8f, 0.2f); // Green
                                break;
                            case PlayerType.Hardcore:
                                legendColor = new Color(1.0f, 0.6f, 0.2f); // Orange
                                break;
                            default:
                                legendColor = Color.gray;
                                break;
                        }

                        // Draw color box
                        Rect colorRect = EditorGUILayout.GetControlRect(false, 16, GUILayout.Width(16));
                        EditorGUI.DrawRect(colorRect, legendColor);

                        // Draw type name
                        EditorGUILayout.LabelField(type.ToString(), GUILayout.Width(70));

                        GUILayout.Space(10);
                    }

                    GUILayout.FlexibleSpace(); // Center the legend
                    EditorGUILayout.EndHorizontal();
                }

                EditorGUILayout.Space(5);

                // Display time to reach max level
                if (simulationResults.Count > 0 && simulationResults[simulationResults.Count - 1].Level == MaxLevel)
                {
                    var maxLevelResult = simulationResults[simulationResults.Count - 1];

                    EditorGUILayout.LabelField($"Time to Max Level ({MaxLevel}):", EditorStyles.boldLabel);

                    if (maxLevelResult.TotalDays < 1)
                        EditorGUILayout.LabelField($"{maxLevelResult.TotalHours:F1} hours", EditorStyles.boldLabel);
                    else if (maxLevelResult.TotalDays < 30)
                        EditorGUILayout.LabelField($"{maxLevelResult.TotalDays:F1} days", EditorStyles.boldLabel);
                    else if (maxLevelResult.TotalDays < 365)
                        EditorGUILayout.LabelField($"{maxLevelResult.TotalDays / 30:F1} months", EditorStyles.boldLabel);
                    else
                        EditorGUILayout.LabelField($"{maxLevelResult.TotalDays / 365:0.0} years", EditorStyles.boldLabel); // Force one decimal place

                    EditorGUILayout.LabelField($"Estimated Completion: {maxLevelResult.EstimatedDate.ToString("MMMM d, yyyy")}", EditorStyles.boldLabel);
                }

                EditorGUILayout.EndVertical();
            }
            else
            {
                EditorGUILayout.HelpBox("No simulation results available. Please check your settings and run the simulation.", MessageType.Warning);
            }

            EditorGUILayout.EndScrollView();
            EditorGUILayout.EndVertical(); // End the dark background vertical
        }

        /// <summary>
        /// Updates the cached player type comparison data
        /// </summary>
        private void UpdatePlayerTypeComparison()
        {
            cachedPlayerTypeDays.Clear();

            // Store current settings
            PlayerType currentType = selectedPlayerType;
            float currentPlayTime = playTimePerDay;
            float currentActionsPerHour = actionsPerHour;
            float currentXpPerAction = xpPerAction;

            // Calculate for each player type
            foreach (PlayerType type in Enum.GetValues(typeof(PlayerType)))
            {
                // Get settings for this player type
                if (playerTypeSettings.TryGetValue(type, out PlayerTypeSettings settings))
                {
                    // Temporarily switch to this player type's settings
                    playTimePerDay = settings.playTimePerDay;
                    actionsPerHour = settings.actionsPerHour;
                    xpPerAction = settings.xpPerAction;

                    // Run a simulation for this player type
                    List<SimulationResult> typeResults = RunSimulationForPlayerType();

                    // Get the max level result
                    if (typeResults.Count > 0 && typeResults[typeResults.Count - 1].Level == MaxLevel)
                    {
                        cachedPlayerTypeDays[type] = typeResults[typeResults.Count - 1].TotalDays;
                    }
                    else
                    {
                        cachedPlayerTypeDays[type] = 0;
                    }
                }
            }

            // Restore original settings
            selectedPlayerType = currentType;
            playTimePerDay = currentPlayTime;
            actionsPerHour = currentActionsPerHour;
            xpPerAction = currentXpPerAction;
        }

        /// <summary>
        /// Updates the cached reward preview data - shows number of actions needed to reach each level
        /// </summary>
        private void UpdateRewardPreviewCache(int[] previewLevels)
        {
            cachedRewardPreview.Clear();

            // Get all categories from the current reward type
            var allCategories = currentRewardType == RewardType.Percentage
                ? rewardCategories.Keys.ToList()
                : hardValueRewardCategories.Keys.ToList();

            foreach (string categoryName in allCategories)
            {
                var levelActionsNeeded = new Dictionary<int, int>();

                foreach (int level in previewLevels)
                {
                    if (level <= MaxLevel)
                    {
                        // Get the level index
                        int levelIndex = level - StartingLevel;

                        // Ensure valid index
                        if (levelIndex >= 0 && levelIndex < experienceRequirements.Count)
                        {
                            // Calculate the XP reward at this level using the unified method
                            int expRequired = experienceRequirements[levelIndex];
                            int rewardPerAction = CalculateExperienceRewardForCategory(categoryName, expRequired, level);

                            // Calculate how many actions are needed to reach this level
                            int actionsNeeded = rewardPerAction > 0 ? Mathf.CeilToInt((float)expRequired / rewardPerAction) : 0;
                            levelActionsNeeded[level] = actionsNeeded;
                        }
                        else
                        {
                            levelActionsNeeded[level] = 0;
                        }
                    }
                }

                cachedRewardPreview[categoryName] = levelActionsNeeded;
            }
        }
        #endregion
    }
}
