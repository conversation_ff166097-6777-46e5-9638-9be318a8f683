using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Polynomial algorithm
    /// </summary>
    public static class PolynomialAlgorithmExtension
    {
        // Default parameters
        private const float DefaultZeroBaseMultiplier = 1.05f;
        private const float DefaultPolynomialCoefficient = 0.002f;
        private const float DefaultPolynomialPower = 2.5f;

        /// <summary>
        /// Calculates the next experience requirement using the polynomial formula method
        /// </summary>
        public static int CalculatePolynomialRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);

            // Calculate the polynomial growth factor
            // This creates a curve that increases the multiplier as a power of the level
            float levelPower = Mathf.Pow(currentLevel, DefaultPolynomialPower); // Use a power between quadratic and cubic
            float growthFactor = 1f + DefaultPolynomialCoefficient * levelPower;

            // Calculate the actual multiplier with polynomial growth
            float actualMultiplier;

            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure polynomial pattern
                // with a smaller base to avoid excessive growth
                actualMultiplier = DefaultZeroBaseMultiplier * growthFactor;
            }
            else
            {
                // Apply the polynomial growth to the effective multiplier
                actualMultiplier = effectiveMultiplier * growthFactor;
            }

            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);

            // Allow negative multipliers to reduce XP requirements (easier levels)
            // Only enforce a minimum of 1 XP to prevent zero or negative XP requirements
            return Mathf.Max(nextRequirement, 1);
        }

        /// <summary>
        /// Calculates raw formula values for visualization using the polynomial formula method
        /// </summary>
        public static List<float> CalculatePolynomialRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();

            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);

            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the polynomial growth factor
                float levelPower = Mathf.Pow(level, DefaultPolynomialPower);
                float growthFactor = 1f + DefaultPolynomialCoefficient * levelPower;

                // Calculate the actual multiplier with polynomial growth
                float rawValue;

                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure polynomial pattern
                    rawValue = DefaultZeroBaseMultiplier * growthFactor;
                }
                else
                {
                    // Apply the polynomial growth to the effective multiplier
                    rawValue = effectiveMultiplier * growthFactor;
                }

                // Add to the list
                rawValues.Add(rawValue);
            }

            return rawValues;
        }
    }
}
