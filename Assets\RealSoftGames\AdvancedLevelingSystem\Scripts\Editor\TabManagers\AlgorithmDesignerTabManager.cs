using UnityEditor;
using UnityEngine;
using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using RealSoftGames.AdvancedLevelingSystem;

// The namespace doesn't match the folder structure, but we're keeping it consistent with the rest of the codebase
namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Tab manager for creating and editing custom leveling algorithms by drawing points on a graph or using code
    /// </summary>
    public class AlgorithmDesignerTabManager : ITabManager
    {
        private readonly EditorWindow window;
        private GUIStyle headerStyle;
        private GUIStyle subHeaderStyle;
        private GUIStyle buttonStyle;
        private GUIStyle toolbarButtonStyle;
        private GUIStyle sectionBoxStyle;

        // Flag to track if we're editing an existing pattern
        private bool isEditingExistingPattern = false;
        private string currentPatternPath = string.Empty;

        // Pattern selection dropdown
        private string[] patternNames = new string[] { "None" };
        private string[] patternPaths = new string[] { "" };
        private int selectedPatternIndex = 0;

        // Pattern data
        private LevelingAlgorithmBase algorithmData;
        private List<Vector2> points = new();

        // Scroll positions for different sections
        private Vector2 detailedStatsScrollPosition = Vector2.zero;
        private Vector2 experienceRewardsScrollPosition = Vector2.zero;

        // Graph settings
        private Rect graphRect;
        private readonly float graphPadding = 80f;

        // Preview settings
        private int startingLevel = 1;
        private int maxLevel = 30;
        private float levelUpMultiplier = 1.1f;
        private int startingExperience = 250;
        private LevelingCurvePreview curvePreview;

        // UI settings
        private bool showHelp = false;

        // Graph display settings
        private bool showGridLines = true;
        private bool showPointValues = true;
        private bool useCustomColors = false;
        private Color customCurveColor = new(0.2f, 0.6f, 1.0f, 1.0f);

        // Scaling options
        private float yAxisScale = 1.0f;
        private float yAxisMin = -5.0f;
        private float yAxisMax = 5.0f;
        private bool onePointPerLevel = false;
        private bool autoAdjustPoints = true;

        // Point selection and dragging
        private int selectedPointIndex = -1;
        private readonly bool isDragging = false;

        // Experience requirements for graph display
        private List<int> experienceRequirements = new();

        // Template intensity for curve templates
        private float templateIntensity = 1.0f;

        // Curve templates and smoothing methods
        public enum CurveTemplate
        {
            None,
            Linear,
            Bell,
            SCurve,
            Exponential,
            Logarithmic,
            Sinusoidal,
            Heartbeat,
            Sawtooth,
            Step,
            Plateau
        }

        public enum SmoothingMethod
        {
            None,
            Light,
            Medium,
            Strong,
            Bezier,
            CatmullRom
        }

        private CurveTemplate selectedTemplate = CurveTemplate.None;
        private SmoothingMethod selectedSmoothingMethod = SmoothingMethod.None;

        // Helper classes
        private AlgorithmDesignerFileOperations fileOperations;
        private AlgorithmDesignerGraphRenderer graphRenderer;
        private AlgorithmDesignerInputHandler inputHandler;
        private AlgorithmDesignerPatternCreator patternCreator;
        private AlgorithmDesignerDataCalculator dataCalculator;

        // Cache for sorted points to avoid repeated sorting operations
        private List<Vector2> cachedSortedPoints = new();
        private bool pointsNeedSorting = true;

        // Performance optimization - cache expensive calculations
        private bool needsDataRecalculation = true;
        private bool needsPreviewRecalculation = true;
        private float lastLevelUpMultiplier;
        private int lastStartingExperience;
        private int lastStartingLevel;
        private int lastMaxLevel;
        private int lastPointsHash;

        // Table pagination for performance
        private const int MAX_TABLE_ROWS = 20;
        private int tableStartIndex = 0;

        // Tab properties
        public string TabName => "Algorithm Designer";

        public AlgorithmDesignerTabManager(EditorWindow window)
        {
            this.window = window;
        }

        /// <summary>
        /// Initializes all helper classes and wires up their events
        /// </summary>
        private void InitializeHelperClasses()
        {
            // Initialize the file operations helper
            fileOperations = new AlgorithmDesignerFileOperations();
            fileOperations.OnPatternLoaded += (pattern, path) => {
                algorithmData = pattern;
                currentPatternPath = path;
                points = new List<Vector2>(pattern.points);
                pointsNeedSorting = true;
                window.Repaint();
            };

            // Initialize the graph renderer
            graphRenderer = new AlgorithmDesignerGraphRenderer();

            // Initialize the input handler
            inputHandler = new AlgorithmDesignerInputHandler(window, graphRenderer);
            inputHandler.OnPointAdded += (point) => {
                points.Add(point);
                pointsNeedSorting = true;
                needsDataRecalculation = true;
                needsPreviewRecalculation = true;
                UpdateDataFromPoints();
                window.Repaint();
            };
            inputHandler.OnPointRemoved += (index) => {
                if (index >= 0 && index < points.Count)
                {
                    points.RemoveAt(index);
                    pointsNeedSorting = true;
                    needsDataRecalculation = true;
                    needsPreviewRecalculation = true;
                    UpdateDataFromPoints();
                    window.Repaint();
                }
            };
            inputHandler.OnPointMoved += (index, newPosition) => {
                if (index >= 0 && index < points.Count)
                {
                    points[index] = newPosition;
                    pointsNeedSorting = true;
                    needsDataRecalculation = true;
                    needsPreviewRecalculation = true;
                    UpdateDataFromPoints();
                    window.Repaint();
                }
            };

            // Initialize the pattern creator
            patternCreator = new AlgorithmDesignerPatternCreator();
            patternCreator.OnPointsGenerated += (newPoints) => {
                points.Clear();
                points.AddRange(newPoints);
                pointsNeedSorting = true;
                needsDataRecalculation = true;
                needsPreviewRecalculation = true;
                UpdateDataFromPoints();
                window.Repaint();
            };

            // Wire up the OnPointsReplaced event
            inputHandler.OnPointsReplaced += (newPoints) => {
                points.Clear();
                points.AddRange(newPoints);
                pointsNeedSorting = true;
                needsDataRecalculation = true;
                needsPreviewRecalculation = true;
                UpdateDataFromPoints();
                window.Repaint();
            };

            // Initialize the data calculator
            dataCalculator = new AlgorithmDesignerDataCalculator();
            dataCalculator.OnDataCalculated += (expRequirements, rawValues) => {
                // Data calculated - no need to repaint immediately as it will be handled by the calling method
            };
        }

        public void OnEnable()
        {
            // Initialize helper classes
            InitializeHelperClasses();

            // Check if the AlgorithmDatabase exists and is properly populated
            CheckAndFixAlgorithmDatabase();

            // Initialize algorithm data if needed
            if (algorithmData == null)
            {
                algorithmData = AlgorithmFactory.CreateDrawnPatternAlgorithm();

                // Add some default points
                algorithmData.points = new() {
                    new(0.0f, 1.1f),
                    new(0.25f, 1.15f),
                    new(0.5f, 1.2f),
                    new(0.75f, 1.25f),
                    new(1.0f, 1.3f)
                };

                // Copy points to our working list
                points = new List<Vector2>(algorithmData.points);
            }

            // Initialize the curve preview
            curvePreview ??= new LevelingCurvePreview();

            // Initialize styles
            InitializeStyles();

            // Check if the ScriptableAlgorithmRegistry is properly initialized
            CheckAndFixScriptableAlgorithmRegistry();

            // Find all existing patterns
            FindExistingPatterns();

            // Set up data calculator with current settings
            dataCalculator.StartingLevel = startingLevel;
            dataCalculator.MaxLevel = maxLevel;
            dataCalculator.LevelUpMultiplier = levelUpMultiplier;
            dataCalculator.StartingExperience = startingExperience;

            // Calculate experience requirements for the current algorithm
            dataCalculator.CalculateExperienceRequirements(algorithmData);

            // No need to set up toolbar buttons - they are handled by the EditorWindowTemplate
        }

        /// <summary>
        /// Checks if the AlgorithmDatabase exists and is properly populated, and fixes it if needed
        /// </summary>
        private void CheckAndFixAlgorithmDatabase()
        {
            // Check if the DifficultyRatingDatabase exists
            CheckAndFixDifficultyRatingDatabase();

            // Load the algorithm database
            AlgorithmDatabase database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");

            // Check if the database exists
            if (database == null)
            {
                Debug.LogWarning("AlgorithmDatabase not found. Creating a new one...");
                CreateDefaultAlgorithmsMenu.CreateAlgorithmDatabase();

                // Reload the database
                database = Resources.Load<AlgorithmDatabase>("AlgorithmDatabase");

                if (database == null)
                {
                    Debug.LogError("Failed to create AlgorithmDatabase. Please create it manually.");
                    return;
                }
            }

            // Complete the database check and fix process
            CompleteAlgorithmDatabaseCheck(database);
        }

        /// <summary>
        /// Checks if the ScriptableAlgorithmRegistry is properly initialized and fixes it if needed
        /// </summary>
        private void CheckAndFixScriptableAlgorithmRegistry()
        {
            // Force initialize the registry
            ScriptableAlgorithmRegistry.Initialize(true);

            // Check if the registry has any algorithms
            if (ScriptableAlgorithmRegistry.GetAlgorithmCount() == 0)
            {
                Debug.LogWarning("ScriptableAlgorithmRegistry has no algorithms. Forcing reload from database...");

                // Force reload from database
                ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

                // Check again
                if (ScriptableAlgorithmRegistry.GetAlgorithmCount() == 0)
                {
                    Debug.LogWarning("Still no algorithms in registry. Checking if database needs to be fixed...");

                    // Check and fix the algorithm database
                    CheckAndFixAlgorithmDatabase();

                    // Try one more time
                    ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

                    if (ScriptableAlgorithmRegistry.GetAlgorithmCount() == 0)
                    {
                        Debug.LogError("Failed to load algorithms into registry. Try creating default algorithms manually.");
                    }
                }
            }
        }

        /// <summary>
        /// Checks if the DifficultyRatingDatabase exists and creates it if needed
        /// </summary>
        private void CheckAndFixDifficultyRatingDatabase()
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");

            // Check if the database exists
            if (ratingDB == null)
            {
                Debug.LogWarning("DifficultyRatingDatabase not found. Creating a new one...");

                // Create the database
                ratingDB = ScriptableObject.CreateInstance<DifficultyRatingDatabase>();

                // Save the database first
                string resourcesPath = "Assets/RealSoftGames/AdvancedLevelingSystem/Resources";

                // Create the directory if it doesn't exist
                if (!AssetDatabase.IsValidFolder(resourcesPath))
                {
                    string parentPath = "Assets/RealSoftGames/AdvancedLevelingSystem";
                    AssetDatabase.CreateFolder(parentPath, "Resources");
                }

                string databasePath = resourcesPath + "/DifficultyRatingDatabase.asset";
                AssetDatabase.CreateAsset(ratingDB, databasePath);

                // Create default difficulty ratings
                CreateDefaultDifficultyRatings(ratingDB);

                // Save all assets
                EditorUtility.SetDirty(ratingDB);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                Debug.Log($"Created DifficultyRatingDatabase with default difficulty ratings at {databasePath}");
            }
            else if (ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogWarning("DifficultyRatingDatabase exists but is empty. Adding default difficulty ratings...");

                // Create default difficulty ratings
                CreateDefaultDifficultyRatings(ratingDB);

                // Save all assets
                EditorUtility.SetDirty(ratingDB);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                Debug.Log("Added default difficulty ratings to existing DifficultyRatingDatabase.");
            }
        }

        /// <summary>
        /// Creates default difficulty ratings for a new DifficultyRatingDatabase
        /// </summary>
        private void CreateDefaultDifficultyRatings(DifficultyRatingDatabase database)
        {
            // Create default difficulty ratings as sub-assets of the database
            CreateDifficultyRatingAsSubAsset(database, "Very Easy", Color.green, 1, "Extremely gentle progression suitable for casual games.");
            CreateDifficultyRatingAsSubAsset(database, "Easy", new Color(0.5f, 1f, 0.5f), 2, "Smooth progression with moderate challenges.");
            CreateDifficultyRatingAsSubAsset(database, "Medium", Color.yellow, 3, "Balanced progression with regular challenges.");
            CreateDifficultyRatingAsSubAsset(database, "Hard", new Color(1f, 0.5f, 0f), 4, "Steep progression requiring dedication and skill.");
            CreateDifficultyRatingAsSubAsset(database, "Very Hard", Color.red, 5, "Extremely challenging progression for dedicated players.");
        }

        /// <summary>
        /// Creates a difficulty rating as a sub-asset of the database
        /// </summary>
        private void CreateDifficultyRatingAsSubAsset(DifficultyRatingDatabase database, string name, Color color, int stars, string description)
        {
            // Create the rating
            DifficultyRating rating = ScriptableObject.CreateInstance<DifficultyRating>();
            rating.name = name; // Set the asset name
            rating.uniqueID = System.Guid.NewGuid().ToString();
            rating.ratingName = name;
            rating.ratingColor = color;
            rating.stars = stars;
            rating.description = description;

            // Add it to the database
            database.AddDifficultyRating(rating);

            // Save it as a sub-asset of the database
            AssetDatabase.AddObjectToAsset(rating, database);
            EditorUtility.SetDirty(rating);

            Debug.Log($"Created difficulty rating '{name}' as sub-asset of DifficultyRatingDatabase");
        }



        /// <summary>
        /// Completes the database check and fix process
        /// </summary>
        private void CompleteAlgorithmDatabaseCheck(AlgorithmDatabase database)
        {
            // Check if the database is empty
            if (database.algorithms == null || database.algorithms.Count == 0)
            {
                Debug.LogWarning("AlgorithmDatabase is empty. Creating default algorithms...");
                CreateDefaultAlgorithmsMenu.CreateDefaultAlgorithms();
            }
            else
            {
                // Check if the database contains all algorithm assets in the Resources/Algorithms folder
                CheckAndLinkAlgorithmAssets(database);

                // Check for null entries in the database
                bool hasNullEntries = false;
                foreach (var algorithm in database.algorithms)
                {
                    if (algorithm == null)
                    {
                        hasNullEntries = true;
                        break;
                    }
                }

                if (hasNullEntries)
                {
                    Debug.LogWarning("AlgorithmDatabase contains null entries. Cleaning up...");

                    // Remove null entries
                    database.algorithms.RemoveAll(a => a == null);

                    // Save the database
                    EditorUtility.SetDirty(database);
                    AssetDatabase.SaveAssets();
                }
            }
        }

        /// <summary>
        /// Checks if all algorithm assets in the Resources/Algorithms folder are linked to the database
        /// </summary>
        private void CheckAndLinkAlgorithmAssets(AlgorithmDatabase database)
        {
            // Find all LevelingAlgorithmBase assets in the project
            string[] guids = AssetDatabase.FindAssets("t:LevelingAlgorithmBase");

            if (guids.Length == 0)
            {
                Debug.LogWarning("No algorithm assets found in the project.");
                return;
            }

            bool databaseModified = false;

            // Process each found asset
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                LevelingAlgorithmBase algorithm = AssetDatabase.LoadAssetAtPath<LevelingAlgorithmBase>(path);

                if (algorithm != null)
                {
                    // Fix missing difficulty rating if needed
                    if (algorithm.difficultyRating == null)
                    {
                        FixMissingDifficultyRating(algorithm);
                        EditorUtility.SetDirty(algorithm);
                    }

                    // Check if this algorithm is already in the database
                    bool alreadyInDatabase = false;
                    foreach (var existingAlgo in database.algorithms)
                    {
                        if (existingAlgo == algorithm)
                        {
                            alreadyInDatabase = true;
                            break;
                        }
                    }

                    // Add to database if not already there
                    if (!alreadyInDatabase)
                    {
                        Debug.Log($"Adding algorithm '{algorithm.algorithmName}' to the database.");
                        database.AddAlgorithm(algorithm);
                        databaseModified = true;
                    }
                }
            }

            // Save the database if modified
            if (databaseModified)
            {
                EditorUtility.SetDirty(database);
                AssetDatabase.SaveAssets();
                Debug.Log("AlgorithmDatabase updated with missing algorithm assets.");
            }
        }

        /// <summary>
        /// Fixes algorithms with missing difficulty ratings
        /// </summary>
        private void FixMissingDifficultyRating(LevelingAlgorithmBase algorithm)
        {
            // Load the difficulty rating database
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");

            if (ratingDB == null || ratingDB.difficultyRatings.Count == 0)
            {
                Debug.LogError("Difficulty Rating Database not found or empty. Cannot fix missing difficulty ratings.");
                return;
            }

            // Find the "Medium" difficulty rating (or use the first one if "Medium" doesn't exist)
            DifficultyRating mediumRating = null;

            foreach (var rating in ratingDB.difficultyRatings)
            {
                if (rating.ratingName == "Medium")
                {
                    mediumRating = rating;
                    break;
                }
            }

            // If "Medium" wasn't found, use the first rating
            if (mediumRating == null && ratingDB.difficultyRatings.Count > 0)
            {
                mediumRating = ratingDB.difficultyRatings[0];
            }

            // Assign the difficulty rating
            if (mediumRating != null)
            {
                Debug.Log($"Fixing missing difficulty rating for algorithm '{algorithm.algorithmName}'. Assigning '{mediumRating.ratingName}' difficulty.");
                algorithm.difficultyRating = mediumRating;
            }
        }

        /// <summary>
        /// Fixes all algorithms with missing difficulty ratings in the project
        /// </summary>
        private void FixAllMissingDifficultyRatings()
        {
            // Find all LevelingAlgorithmBase assets in the project
            string[] guids = AssetDatabase.FindAssets("t:LevelingAlgorithmBase");

            if (guids.Length == 0)
            {
                Debug.LogWarning("No algorithm assets found in the project.");
                return;
            }

            int fixedCount = 0;

            // Process each found asset
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                LevelingAlgorithmBase algorithm = AssetDatabase.LoadAssetAtPath<LevelingAlgorithmBase>(path);

                if (algorithm != null && algorithm.difficultyRating == null)
                {
                    FixMissingDifficultyRating(algorithm);
                    EditorUtility.SetDirty(algorithm);
                    fixedCount++;
                }
            }

            // Save all assets if any were modified
            if (fixedCount > 0)
            {
                AssetDatabase.SaveAssets();
                Debug.Log($"Fixed {fixedCount} algorithms with missing difficulty ratings.");
            }
            else
            {
                Debug.Log("No algorithms with missing difficulty ratings found.");
            }
        }

        /// <summary>
        /// Finds all existing pattern algorithms in the project
        /// </summary>
        private void FindExistingPatterns()
        {
            fileOperations.FindExistingPatterns(currentPatternPath);

            // Update local references to the pattern arrays
            patternNames = fileOperations.PatternNames;
            patternPaths = fileOperations.PatternPaths;
            selectedPatternIndex = fileOperations.SelectedPatternIndex;
        }



        /// <summary>
        /// Finds all coded algorithm types in the project and converts them to scriptable objects
        /// </summary>
        public void ConvertAllCodedAlgorithms()
        {
            // Delegate to the file operations helper
            fileOperations.ConvertAllCodedAlgorithms();

            // Refresh the pattern list after conversion
            FindExistingPatterns();
        }

        /// <summary>
        /// Toggles the help display
        /// </summary>
        public void ToggleHelp()
        {
            showHelp = !showHelp;
            window.Repaint();
        }

        public void OnDisable()
        {
            // No cleanup needed for direct input handling
        }

        /// <summary>
        /// Handles input events for the graph using the InputHandler
        /// </summary>

        private void HandleDirectInputEvents(Rect innerRect)
        {
            // Set the Y-axis range for the input handler
            inputHandler.SetYAxisRange(yAxisMin, yAxisMax);

            // Handle input events
            inputHandler.HandleInputEvents(innerRect, points);

            // Draw the coordinate editor for the selected point
            inputHandler.DrawCoordinateEditorForSelectedPoint(innerRect, points);

            // Update the selected point index
            selectedPointIndex = inputHandler.SelectedPointIndex;
        }

        // Helper method to get sorted points with caching
        private List<Vector2> GetSortedPoints()
        {
            return patternCreator.GetSortedPoints(points, ref cachedSortedPoints, ref pointsNeedSorting);
        }

        public void OnDestroy()
        {
            // Clean up resources if needed
            if (algorithmData != null && !AssetDatabase.Contains(algorithmData))
            {
                UnityEngine.Object.DestroyImmediate(algorithmData);
            }
        }

        public void Update()
        {
            // Handle continuous updates if needed
            if (isDragging && selectedPointIndex >= 0 && selectedPointIndex < points.Count)
            {
                window.Repaint();
            }
        }

        private void InitializeStyles()
        {
            // Only initialize styles during OnGUI to avoid errors
            // We'll check again in OnGUI to make sure styles are initialized
        }

        private void EnsureStylesInitialized()
        {
            // Initialize header style
            headerStyle ??= new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 16,
                alignment = TextAnchor.MiddleLeft,
                margin = new RectOffset(5, 5, 5, 5)
            };

            // Initialize subheader style
            subHeaderStyle ??= new GUIStyle(EditorStyles.boldLabel)
            {
                fontSize = 14,
                alignment = TextAnchor.MiddleLeft,
                margin = new RectOffset(5, 5, 5, 5)
            };

            // Initialize button style
            buttonStyle ??= new GUIStyle(GUI.skin.button)
            {
                fontStyle = FontStyle.Bold,
                padding = new RectOffset(10, 10, 6, 6)
            };

            // Initialize toolbar button style
            toolbarButtonStyle ??= new GUIStyle(EditorStyles.miniButton)
            {
                padding = new RectOffset(8, 8, 2, 2),
                margin = new RectOffset(2, 2, 0, 0),
                fixedHeight = 24
            };

            // Initialize section box style
            sectionBoxStyle ??= new GUIStyle(EditorStyles.helpBox)
            {
                padding = new RectOffset(10, 10, 10, 10),
                margin = new RectOffset(0, 0, 5, 5)
            };
        }



        public void OnGUI()
        {
            EnsureStylesInitialized();

            // Use the helper classes to render the UI
            DrawAlgorithmDesignerUI();
        }

        /// <summary>
        /// Draws the Algorithm Designer UI using the helper classes
        /// </summary>
        private void DrawAlgorithmDesignerUI()
        {
            EditorGUILayout.BeginVertical();

            // No need for a separate scroll view as the main body already has one

            // Calculate the main body width for later use
            float mainBodyWidth = EditorWindowTemplate.MainBodyRect.width - 40; // Account for padding
            float graphWidth = mainBodyWidth;

            #region Help Text
            // Help text
            if (showHelp)
            {
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);
                EditorGUILayout.LabelField("Algorithm Designer Help", EditorStyles.boldLabel);
                EditorGUILayout.HelpBox(
                    "- Click on the graph to add points\n" +
                    "- Click and drag points to move them\n" +
                    "- Double-click a point to remove it\n" +
                    "- Right-click for context menu options\n" +
                    "- The Y-axis represents the level-up multiplier (-5 to +5):\n" +
                    "  • Positive values (0 to +5): Increase XP requirements\n" +
                    "  • Negative values (-5 to 0): Decrease XP requirements\n" +
                    "  • Zero (0): No change in XP requirement\n" +
                    "- The X-axis represents the progression from start to max level\n" +
                    "- Use the toolbar buttons for quick actions\n" +
                    "- Customize the pattern appearance in the Display Settings section",
                    MessageType.Info);
                EditorGUILayout.EndVertical();
            }
            EditorGUILayout.Space(10);

            #endregion

            #region Pattern Settings
            // Pattern settings section with improved layout
            EditorGUILayout.BeginVertical(sectionBoxStyle);

            // Section header
            GUIStyle sectionHeaderStyle = new(EditorStyles.boldLabel) {
                fontSize = 14,
                margin = new RectOffset(0, 0, 5, 5)
            };
            EditorGUILayout.LabelField("Algorithm Settings", sectionHeaderStyle);
            EditorGUILayout.Space(5);

            // Pattern selection dropdown
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Existing Algorithms", GUILayout.Width(120));

            // Use the searchable popup from Utilities
            selectedPatternIndex = Utilities.SearchablePopup(
                selectedPatternIndex,
                "Select Pattern",
                patternNames,
                (index) => {
                    Debug.Log($"Pattern selection changed from {selectedPatternIndex} to {index}");
                    if (index != selectedPatternIndex)
                    {
                        if (index > 0 && index < patternPaths.Length)
                        {
                            // Load the selected pattern
                            string path = patternPaths[index];
                            Debug.Log($"Loading pattern from path: {path}");
                            fileOperations.LoadPatternByPath(path);
                            isEditingExistingPattern = true;
                        }
                        else if (index == 0)
                        {
                            // "None" selected - show a menu to create a new algorithm of either type
                            GenericMenu menu = new GenericMenu();
                            menu.AddItem(new GUIContent("Create New Drawn Pattern"), false, () => {
                                Debug.Log("Creating new drawn pattern");
                                algorithmData = AlgorithmFactory.CreateDrawnPatternAlgorithm();
                                points = new List<Vector2>(algorithmData.points);
                                currentPatternPath = "";
                                isEditingExistingPattern = false;
                                window.Repaint();
                            });
                            menu.AddItem(new GUIContent("Create New Coded Formula"), false, () => {
                                Debug.Log("Creating new coded formula");
                                algorithmData = AlgorithmFactory.CreateCodedFormulaAlgorithm();
                                points = new List<Vector2>(); // Empty points list for coded formula
                                currentPatternPath = "";
                                isEditingExistingPattern = false;
                                window.Repaint();
                            });
                            menu.ShowAsContext();
                        }
                    }
                }
            );

            // Refresh button
            if (GUILayout.Button("↻", GUILayout.Width(25)))
            {
                // Force refresh the algorithm registry
                ScriptableAlgorithmRegistry.Initialize(true);

                // Find existing patterns
                FindExistingPatterns();
            }

            // Force refresh registry button
            if (GUILayout.Button("Force Refresh Registry", GUILayout.Width(150)))
            {
                // Force refresh the algorithm registry
                ScriptableAlgorithmRegistry.ForceLoadFromDatabase();

                // Find existing patterns
                FindExistingPatterns();
            }

            EditorGUILayout.EndHorizontal();



            EditorGUILayout.Space(5);

            // Basic settings in a clean layout
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            // Algorithm name
            string newName = EditorGUILayout.TextField("Algorithm Name", algorithmData.algorithmName);
            if (newName != algorithmData.algorithmName)
            {
                algorithmData.algorithmName = newName;
            }

            // Display algorithm type
            EditorGUI.BeginDisabledGroup(true);
            EditorGUILayout.EnumPopup("Algorithm Type", algorithmData.algorithmType);
            EditorGUI.EndDisabledGroup();

            // Description
            EditorGUILayout.LabelField("Description", GUILayout.Width(120));
            string newDescription = EditorGUILayout.TextArea(algorithmData.description, GUILayout.Height(60));
            if (newDescription != algorithmData.description)
            {
                algorithmData.description = newDescription;
            }

            EditorGUILayout.EndVertical();
            EditorGUILayout.Space(5);

            // Formula explanation in its own box
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("Formula Explanation", EditorStyles.boldLabel);

            // Show different formula explanation based on algorithm type
            if (algorithmData.algorithmType == AlgorithmEnums.AlgorithmType.DrawnPattern)
            {
                EditorGUILayout.LabelField("Formula: Custom drawn curve");
            }
            else // CodedFormula
            {
                // Build a formula string based on the current parameters
                string formulaString = "Experience = Previous * " + algorithmData.baseMultiplier.ToString("F2");

                if (algorithmData.levelMultiplierFactor > 0)
                {
                    formulaString += $" + {algorithmData.levelMultiplierFactor:F3} * Level";
                }

                if (algorithmData.useSineWave)
                {
                    formulaString += $" + {algorithmData.sineAmplitude:F2} * sin(Level * π/{algorithmData.sinePeriod:F1})";
                }

                if (algorithmData.useHeartbeat)
                {
                    formulaString += $" + {algorithmData.heartbeatAmplitude:F2} * heartbeat(Level, {algorithmData.heartbeatPeriod:F1})";
                }

                if (algorithmData.useZigzag)
                {
                    formulaString += $" + {algorithmData.zigzagAmplitude:F2} * zigzag(Level, {algorithmData.zigzagPeriod:F1})";
                }

                if (algorithmData.useRandomFluctuations)
                {
                    formulaString += $" + {algorithmData.randomAmplitude:F2} * random(-1, 1)";
                }

                EditorGUILayout.LabelField("Formula:", EditorStyles.boldLabel);
                EditorGUILayout.LabelField(formulaString, EditorStyles.wordWrappedLabel);
            }

            string newFormula = EditorGUILayout.TextArea(algorithmData.formulaExplanation, GUILayout.Height(60));
            if (newFormula != algorithmData.formulaExplanation)
            {
                algorithmData.formulaExplanation = newFormula;
            }
            EditorGUILayout.EndVertical();
            EditorGUILayout.Space(10);

            // Show different tools based on algorithm type
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            if (algorithmData.algorithmType == AlgorithmEnums.AlgorithmType.DrawnPattern)
            {
                // DRAWN PATTERN TOOLS
                EditorGUILayout.LabelField("Advanced Pattern Creation Tools", EditorStyles.boldLabel);
                EditorGUILayout.Space(5);

                // Curve Templates
                EditorGUILayout.LabelField("Curve Templates", EditorStyles.boldLabel);
                EditorGUILayout.BeginHorizontal();
            }
            else // CodedFormula
            {
                // CODED FORMULA TOOLS
                EditorGUILayout.LabelField("Formula Parameters", EditorStyles.boldLabel);
                EditorGUILayout.Space(5);

                // Base multiplier
                float newBaseMultiplier = EditorGUILayout.Slider("Base Multiplier", algorithmData.baseMultiplier, 0.5f, 2.0f);
                if (newBaseMultiplier != algorithmData.baseMultiplier)
                {
                    algorithmData.baseMultiplier = newBaseMultiplier;
                    window.Repaint();
                }

                // Level multiplier factor
                float newLevelMultiplierFactor = EditorGUILayout.Slider("Level Factor", algorithmData.levelMultiplierFactor, 0.0f, 0.1f);
                if (newLevelMultiplierFactor != algorithmData.levelMultiplierFactor)
                {
                    algorithmData.levelMultiplierFactor = newLevelMultiplierFactor;
                    window.Repaint();
                }

                // Sine wave parameters
                bool newUseSineWave = EditorGUILayout.Toggle("Use Sine Wave", algorithmData.useSineWave);
                if (newUseSineWave != algorithmData.useSineWave)
                {
                    algorithmData.useSineWave = newUseSineWave;
                    window.Repaint();
                }

                if (algorithmData.useSineWave)
                {
                    EditorGUI.indentLevel++;
                    algorithmData.sineAmplitude = EditorGUILayout.Slider("Amplitude", algorithmData.sineAmplitude, 0.01f, 0.5f);
                    algorithmData.sinePeriod = EditorGUILayout.Slider("Period", algorithmData.sinePeriod, 1f, 20f);
                    EditorGUI.indentLevel--;
                }

                // Heartbeat parameters
                bool newUseHeartbeat = EditorGUILayout.Toggle("Use Heartbeat", algorithmData.useHeartbeat);
                if (newUseHeartbeat != algorithmData.useHeartbeat)
                {
                    algorithmData.useHeartbeat = newUseHeartbeat;
                    window.Repaint();
                }

                if (algorithmData.useHeartbeat)
                {
                    EditorGUI.indentLevel++;
                    algorithmData.heartbeatAmplitude = EditorGUILayout.Slider("Amplitude", algorithmData.heartbeatAmplitude, 0.01f, 0.5f);
                    algorithmData.heartbeatPeriod = EditorGUILayout.Slider("Period", algorithmData.heartbeatPeriod, 1f, 20f);
                    EditorGUI.indentLevel--;
                }

                // Zigzag parameters
                bool newUseZigzag = EditorGUILayout.Toggle("Use Zigzag", algorithmData.useZigzag);
                if (newUseZigzag != algorithmData.useZigzag)
                {
                    algorithmData.useZigzag = newUseZigzag;
                    window.Repaint();
                }

                if (algorithmData.useZigzag)
                {
                    EditorGUI.indentLevel++;
                    algorithmData.zigzagAmplitude = EditorGUILayout.Slider("Amplitude", algorithmData.zigzagAmplitude, 0.01f, 0.5f);
                    algorithmData.zigzagPeriod = EditorGUILayout.Slider("Period", algorithmData.zigzagPeriod, 1f, 20f);
                    EditorGUI.indentLevel--;
                }

                // Random fluctuations
                bool newUseRandomFluctuations = EditorGUILayout.Toggle("Use Random Fluctuations", algorithmData.useRandomFluctuations);
                if (newUseRandomFluctuations != algorithmData.useRandomFluctuations)
                {
                    algorithmData.useRandomFluctuations = newUseRandomFluctuations;
                    window.Repaint();
                }

                if (algorithmData.useRandomFluctuations)
                {
                    EditorGUI.indentLevel++;
                    algorithmData.randomAmplitude = EditorGUILayout.Slider("Amplitude", algorithmData.randomAmplitude, 0.01f, 0.5f);
                    algorithmData.randomSeed = EditorGUILayout.IntField("Random Seed", algorithmData.randomSeed);
                    EditorGUI.indentLevel--;
                }

                EditorGUILayout.HelpBox(
                    "These parameters control the formula used to calculate experience requirements. " +
                    "You can combine multiple effects to create complex progression patterns.",
                    MessageType.Info);

                // Skip the rest of the drawn pattern UI for coded formulas
                EditorGUILayout.EndVertical(); // End the helpBox for coded formula tools
                EditorGUILayout.Space(10);

                // Skip to the preview section for coded formulas
                goto SkipToPreview;
            }

            // Template selection dropdown using searchable popup
            Utilities.SearchableEnumPopup(
                selectedTemplate,
                "Select Template",
                newTemplate => {
                    selectedTemplate = newTemplate;
                    // Store the selection but don't apply it yet
                    // The user will click the Apply Template button
                }
            );

            // Apply button for the template
            if (GUILayout.Button("Apply Template", GUILayout.Width(120)))
            {
                if (selectedTemplate != CurveTemplate.None)
                {
                    ApplyCurveTemplate(selectedTemplate);
                }
            }

            EditorGUILayout.EndHorizontal();

            // Template intensity slider
            if (selectedTemplate != CurveTemplate.None)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Template Intensity", GUILayout.Width(150));

                float newIntensity = EditorGUILayout.Slider(templateIntensity, 0.1f, 3.0f);
                if (newIntensity != templateIntensity)
                {
                    templateIntensity = newIntensity;
                }

                EditorGUILayout.EndHorizontal();
            }

            // Help text for templates
            EditorGUILayout.HelpBox(
                "Select a predefined curve template as a starting point for your pattern. " +
                "Templates provide common curve shapes that you can further customize.",
                MessageType.Info);

            EditorGUILayout.Space(10);

            // Curve Smoothing
            EditorGUILayout.LabelField("Curve Smoothing", EditorStyles.boldLabel);
            EditorGUILayout.BeginHorizontal();

            // Smoothing method selection using searchable popup
            Utilities.SearchableEnumPopup(
                selectedSmoothingMethod,
                "Smoothing Method",
                newSmoothingMethod => {
                    selectedSmoothingMethod = newSmoothingMethod;
                    // Store the selection but don't apply it yet
                    // The user will click the Apply Smoothing button
                }
            );

            // Apply button for smoothing
            if (GUILayout.Button("Apply Smoothing", GUILayout.Width(120)))
            {
                if (selectedSmoothingMethod != SmoothingMethod.None)
                {
                    // Convert our SmoothingMethod enum to the PatternCreator's SmoothingMethod enum
                    var patternCreatorSmoothingMethod = (AlgorithmDesignerPatternCreator.SmoothingMethod)(int)selectedSmoothingMethod;

                    // Apply smoothing using the pattern creator
                    List<Vector2> smoothedPoints = patternCreator.ApplySmoothingToPoints(points, patternCreatorSmoothingMethod);

                    // Update the points
                    points.Clear();
                    points.AddRange(smoothedPoints);

                    // Update the algorithm data
                    UpdateDataFromPoints();

                    // Repaint the window
                    window.Repaint();
                }
            }

            EditorGUILayout.EndHorizontal();

            // Help text for smoothing
            EditorGUILayout.HelpBox(
                "Apply smoothing to refine your hand-drawn pattern. " +
                "Different methods provide varying levels of curve smoothness.",
                MessageType.Info);

            EditorGUILayout.Space(10);



            EditorGUILayout.EndVertical();
            EditorGUILayout.Space(10);

            // Algorithm and Display Settings in a single row
            EditorGUILayout.BeginHorizontal();

            // Algorithm Settings
            EditorGUILayout.BeginVertical(EditorStyles.helpBox, GUILayout.ExpandWidth(true));

            // Algorithm settings header
            EditorGUILayout.LabelField("Algorithm & Display Settings", EditorStyles.boldLabel);
            EditorGUILayout.Space(5);

            // Interpolation method - most important setting
            Utilities.SearchableEnumPopup(
                algorithmData.interpolationMethod,
                "Interpolation Method",
                newMethod => {
                    if (newMethod != algorithmData.interpolationMethod)
                    {
                        algorithmData.interpolationMethod = newMethod;
                        window.Repaint();
                    }
                }
            );

            // Display settings in a more compact layout
            EditorGUILayout.BeginHorizontal();
            bool newShowGridLines = EditorGUILayout.Toggle("Grid Lines", showGridLines);
            if (newShowGridLines != showGridLines)
            {
                showGridLines = newShowGridLines;
                window.Repaint();
            }

            bool newShowPointValues = EditorGUILayout.Toggle("Point Values", showPointValues);
            if (newShowPointValues != showPointValues)
            {
                showPointValues = newShowPointValues;
                window.Repaint();
            }
            EditorGUILayout.EndHorizontal();

            // Custom colors in a horizontal layout
            EditorGUILayout.BeginHorizontal();
            bool newUseCustomColors = EditorGUILayout.Toggle("Custom Colors", useCustomColors);
            if (newUseCustomColors != useCustomColors)
            {
                useCustomColors = newUseCustomColors;
                window.Repaint();
            }

            // Custom curve color
            if (useCustomColors)
            {
                Color newCurveColor = EditorGUILayout.ColorField(customCurveColor, GUILayout.Width(50));
                if (newCurveColor != customCurveColor)
                {
                    customCurveColor = newCurveColor;
                    window.Repaint();
                }
            }
            EditorGUILayout.EndHorizontal();

            // Add a visual separator
            EditorGUILayout.Space(5);
            EditorGUI.DrawRect(EditorGUILayout.GetControlRect(false, 1), new Color(0.5f, 0.5f, 0.5f, 0.5f));
            EditorGUILayout.Space(5);

            // Metadata settings in a horizontal layout
            EditorGUILayout.BeginHorizontal();

            // Difficulty Rating - just a category label
            DifficultyRatingDatabase ratingDB = Resources.Load<DifficultyRatingDatabase>("DifficultyRatingDatabase");
            if (ratingDB != null && ratingDB.difficultyRatings.Count > 0)
            {
                int currentIndex = 0;
                string[] ratingNames = new string[ratingDB.difficultyRatings.Count];

                // Populate the dropdown options and find the current selection
                for (int i = 0; i < ratingDB.difficultyRatings.Count; i++)
                {
                    ratingNames[i] = ratingDB.difficultyRatings[i].ratingName;
                    if (algorithmData.difficultyRating == ratingDB.difficultyRatings[i])
                    {
                        currentIndex = i;
                    }
                }

                // Use the searchable popup from Utilities
                Utilities.SearchablePopup(
                    currentIndex,
                    "Difficulty",
                    ratingNames,
                    (newIndex) => {
                        if (newIndex != currentIndex)
                        {
                            algorithmData.difficultyRating = ratingDB.difficultyRatings[newIndex];
                            EditorUtility.SetDirty(algorithmData);
                        }
                    }
                );
            }
            else
            {
                EditorGUILayout.LabelField("Difficulty Rating Database not found");
            }

            // Removed oscillating pattern flag
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.EndVertical();
            EditorGUILayout.EndHorizontal();
            EditorGUILayout.Space(10);

            // Scaling options in a more compact layout
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            EditorGUILayout.LabelField("Difficulty Scaling", EditorStyles.boldLabel);

            // Y-axis scale (difficulty adjustment) in a more compact layout
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField(new GUIContent("Scale Factor",
                "Adjusts curve intensity (centered around 1.0)"),
                GUILayout.Width(80));

            // Begin change check for the slider
            EditorGUI.BeginChangeCheck();
            float newYAxisScale = GUILayout.HorizontalSlider(yAxisScale, 0.2f, 10.0f, GUILayout.ExpandWidth(true));

            // Begin change check for the float field
            bool sliderChanged = EditorGUI.EndChangeCheck();
            EditorGUI.BeginChangeCheck();
            float fieldValue = EditorGUILayout.FloatField(yAxisScale, GUILayout.Width(40));
            bool fieldChanged = EditorGUI.EndChangeCheck();

            // Update values if either control changed
            if (sliderChanged)
            {
                yAxisScale = newYAxisScale;
                // Force immediate update
                UpdateDataFromPoints();
                CalculateExperienceRequirements();
                window.Repaint();
            }
            else if (fieldChanged)
            {
                // Clamp the value to the valid range
                yAxisScale = Mathf.Clamp(fieldValue, 0.2f, 10.0f);
                // Force immediate update
                UpdateDataFromPoints();
                CalculateExperienceRequirements();
                window.Repaint();
            }

            EditorGUILayout.EndHorizontal();

            // Y-axis scale help text
            EditorGUILayout.HelpBox(
                "Higher values make the curve more pronounced, lower values flatten it. Values are centered around 1.0.",
                MessageType.Info);

            EditorGUILayout.EndVertical();
            EditorGUILayout.Space(10);

            // Bottom row: Y-Axis Range and Point Adjustment side by side
            EditorGUILayout.BeginHorizontal();

            // LEFT COLUMN - Y-Axis Range Options
            EditorGUILayout.BeginVertical(EditorStyles.helpBox, GUILayout.ExpandWidth(true));
            EditorGUILayout.LabelField("Y-Axis Range", EditorStyles.boldLabel);

            // Add explanation about negative values
            EditorGUILayout.HelpBox(
                "Y-axis values represent multipliers that affect experience requirements:\n" +
                "• Positive values (>0): Increase XP requirements\n" +
                "• Negative values (<0): Decrease XP requirements (easier levels)\n" +
                "• Zero (0): No change from previous level",
                MessageType.Info);

            // Y-axis min/max values in a compact layout
            EditorGUILayout.BeginHorizontal();

            // Store the current values
            float currentYAxisMin = yAxisMin;
            float currentYAxisMax = yAxisMax;

            // Create input fields
            yAxisMin = EditorGUILayout.FloatField(yAxisMin, GUILayout.MinWidth(40));
            EditorGUILayout.LabelField("to", GUILayout.Width(20));
            yAxisMax = EditorGUILayout.FloatField(yAxisMax, GUILayout.MinWidth(40));

            if (GUILayout.Button("Apply", GUILayout.Width(60)))
            {
                // Validate the range
                if (yAxisMax > yAxisMin)
                {
                    // Scale existing points to the new range
                    RescalePointsToNewRange(currentYAxisMin, currentYAxisMax, yAxisMin, yAxisMax);

                    // Update the algorithm data
                    UpdateDataFromPoints();
                    window.Repaint();
                }
                else
                {
                    EditorUtility.DisplayDialog("Invalid Range", "Maximum value must be greater than minimum value.", "OK");
                    // Reset to previous values
                    yAxisMin = currentYAxisMin;
                    yAxisMax = currentYAxisMax;
                }
            }
            EditorGUILayout.EndHorizontal();

            // Preset buttons in a more compact grid
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("-5 to +5", GUILayout.Height(20)))
            {
                SetYAxisRange(-5.0f, 5.0f);
            }
            if (GUILayout.Button("-2 to +2", GUILayout.Height(20)))
            {
                SetYAxisRange(-2.0f, 2.0f);
            }
            if (GUILayout.Button("0 to +5", GUILayout.Height(20)))
            {
                SetYAxisRange(0.0f, 5.0f);
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("-10 to +10", GUILayout.Height(20)))
            {
                SetYAxisRange(-10.0f, 10.0f);
            }
            if (GUILayout.Button("-1 to +1", GUILayout.Height(20)))
            {
                SetYAxisRange(-1.0f, 1.0f);
            }
            EditorGUILayout.EndHorizontal();
            EditorGUILayout.EndVertical();

            // RIGHT COLUMN - Point Adjustment Options
            EditorGUILayout.BeginVertical(EditorStyles.helpBox, GUILayout.ExpandWidth(true));
            EditorGUILayout.LabelField("Point Adjustment", EditorStyles.boldLabel);

            // One point per level
            bool newOnePointPerLevel = EditorGUILayout.Toggle(
                new GUIContent("One Point Per Level", "Creates exactly one control point for each level"),
                onePointPerLevel);

            if (newOnePointPerLevel != onePointPerLevel)
            {
                onePointPerLevel = newOnePointPerLevel;

                if (onePointPerLevel)
                {
                    // Convert to one point per level
                    ConvertToOnePointPerLevel();
                }

                window.Repaint();
            }

            // Auto-adjust points
            bool newAutoAdjustPoints = EditorGUILayout.Toggle(
                new GUIContent("Auto-Adjust Points", "Automatically adjusts points when level count changes"),
                autoAdjustPoints);

            if (newAutoAdjustPoints != autoAdjustPoints)
            {
                autoAdjustPoints = newAutoAdjustPoints;
                window.Repaint();
            }

            // Short help text
            EditorGUILayout.HelpBox(
                "Negative Y values create levels that require LESS experience than the previous level.",
                MessageType.Info);

            EditorGUILayout.EndVertical();
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.EndVertical();

            #endregion

            // Label for goto statement from coded formula section
            SkipToPreview:

            #region Preview Settings
            EditorGUILayout.Space(10);

            // Preview settings
            EditorGUILayout.LabelField("Preview Settings", subHeaderStyle);
            EditorGUILayout.Space(5);

            EditorGUI.indentLevel++;

            // Level settings
            int newStartingLevel = EditorGUILayout.IntSlider("Starting Level", startingLevel, 1, 10);
            int newMaxLevel = EditorGUILayout.IntSlider("Max Level", maxLevel, 10, 100);

            // Check if level range changed and auto-adjust if needed
            if ((newStartingLevel != startingLevel || newMaxLevel != maxLevel) && autoAdjustPoints && points.Count > 0)
            {
                startingLevel = newStartingLevel;
                maxLevel = newMaxLevel;

                if (onePointPerLevel)
                {
                    // If one point per level is enabled, regenerate points
                    ConvertToOnePointPerLevel();
                }
                else
                {
                    // Otherwise, just update the X coordinates to match the new range
                    var sortedPoints = points.OrderBy(p => p.x).ToList();
                    points.Clear();

                    for (int i = 0; i < sortedPoints.Count; i++)
                    {
                        // Recalculate X coordinate to maintain relative position
                        float x = (float)i / Mathf.Max(1, sortedPoints.Count - 1);
                        points.Add(new Vector2(x, sortedPoints[i].y));
                    }

                    // Update the algorithm data
                    UpdateDataFromPoints();
                }
            }
            else
            {
                startingLevel = newStartingLevel;
                maxLevel = newMaxLevel;
            }

            startingExperience = EditorGUILayout.IntField("Starting Experience", startingExperience);

            // Level Up Multiplier with change detection (range from -5 to 5, 0 disables multiplier)
            EditorGUI.BeginChangeCheck();
            float newLevelUpMultiplier = EditorGUILayout.Slider("Level Up Multiplier", levelUpMultiplier, -5.0f, 5.0f);
            if (EditorGUI.EndChangeCheck())
            {
                levelUpMultiplier = newLevelUpMultiplier;

                // Force recalculation of experience requirements
                CalculateExperienceRequirements();

                // When Level Up Multiplier changes, ensure all points stay within graph boundaries
                if (points.Count > 0)
                {
                    // Calculate padding for boundaries (5% of the range)
                    float boundaryPadding = (yAxisMax - yAxisMin) * 0.05f;
                    float boundaryMin = yAxisMin + boundaryPadding;
                    float boundaryMax = yAxisMax - boundaryPadding;

                    // Check if any points are outside the boundaries
                    bool needsAdjustment = false;
                    foreach (var point in points)
                    {
                        if (point.y < boundaryMin || point.y > boundaryMax)
                        {
                            needsAdjustment = true;
                            break;
                        }
                    }

                    // If any points are outside boundaries, adjust all points
                    if (needsAdjustment)
                    {
                        // Find min and max Y values
                        float minY = float.MaxValue;
                        float maxY = float.MinValue;

                        foreach (var point in points)
                        {
                            minY = Mathf.Min(minY, point.y);
                            maxY = Mathf.Max(maxY, point.y);
                        }

                        // Calculate scale factor to fit all points within boundaries
                        float currentRange = maxY - minY;
                        float targetRange = boundaryMax - boundaryMin;
                        float scaleFactor = currentRange > 0 ? targetRange / currentRange : 1.0f;

                        // Calculate midpoint for scaling
                        float midY = (minY + maxY) / 2.0f;

                        // Adjust all points to fit within boundaries
                        for (int i = 0; i < points.Count; i++)
                        {
                            float offset = points[i].y - midY;
                            float newY = midY + (offset * scaleFactor);

                            // Ensure the point is within boundaries
                            newY = Mathf.Clamp(newY, boundaryMin, boundaryMax);

                            points[i] = new Vector2(points[i].x, newY);
                        }

                        // Update the algorithm data
                        UpdateDataFromPoints();
                    }
                }
            }

            EditorGUI.indentLevel--;

            EditorGUILayout.Space(10);

            #endregion

            #region Graph Area
            // Graph area - moved below settings
            EditorGUILayout.Space(10);
            EditorGUILayout.LabelField("Pattern Graph", subHeaderStyle);

            // Calculate the graph area inside the main body (reduced height)
            float graphHeight = 300f; // Reduced from 400f to 300f (25% smaller)
            graphRect = GUILayoutUtility.GetRect(mainBodyWidth, graphHeight);

            // Update the graph renderer with the current level range
            graphRenderer.SetLevelRange(startingLevel, maxLevel);

            // Check if we're using level-based X values
            bool usesLevelBasedX = false;
            if (points.Count > 0)
            {
                foreach (var point in points)
                {
                    if (point.x > 1.5f) // If any X value is clearly above 1, we're using level-based X
                    {
                        usesLevelBasedX = true;
                        break;
                    }
                }
            }

            // Set the flag in the graph renderer
            graphRenderer.SetUseLevelBasedX(usesLevelBasedX);

            // Draw the graph
            DrawGraph();

            EditorGUILayout.Space(10);
            #endregion

            #region Pattern Preview and Detailed Stats
            // Preview
            EditorGUILayout.LabelField("Pattern Preview", subHeaderStyle);

            // Only draw preview if needed (performance optimization)
            if (needsPreviewRecalculation || experienceRequirements.Count == 0)
            {
                // Create a temporary algorithm for preview
                var tempData = UnityEngine.Object.Instantiate(algorithmData);
                tempData.Validate();

                // Draw the preview
                try
                {
                    // Create a custom preview that uses our algorithm
                    DrawAlgorithmPreview(tempData, levelUpMultiplier, startingExperience, startingLevel, maxLevel, graphWidth - 20);
                    needsPreviewRecalculation = false;
                }
                catch (Exception ex)
                {
                    EditorGUILayout.HelpBox($"Error drawing preview: {ex.Message}", MessageType.Error);
                }

                // Clean up temporary object
                UnityEngine.Object.DestroyImmediate(tempData);
            }
            else
            {
                // Show cached preview message with refresh button
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.HelpBox("Preview is cached for performance.", MessageType.Info);
                if (GUILayout.Button("Force Refresh", GUILayout.Width(100)))
                {
                    needsPreviewRecalculation = true;
                    window.Repaint();
                }
                EditorGUILayout.EndHorizontal();
            }

            EditorGUILayout.Space(10);

            // Detailed Stats Section
            EditorGUILayout.BeginVertical(sectionBoxStyle);
            EditorGUILayout.LabelField("Detailed Algorithm Statistics", subHeaderStyle);
            EditorGUILayout.Space(5);

            if (experienceRequirements != null && experienceRequirements.Count > 0)
            {
                // Create a scrollable area for the stats
                detailedStatsScrollPosition = EditorGUILayout.BeginScrollView(
                    detailedStatsScrollPosition,
                    GUILayout.Height(200),
                    GUILayout.ExpandWidth(true)
                );

                // Table header
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Level", EditorStyles.boldLabel, GUILayout.Width(50));
                EditorGUILayout.LabelField("XP Required", EditorStyles.boldLabel, GUILayout.Width(100));
                EditorGUILayout.LabelField("Increase", EditorStyles.boldLabel, GUILayout.Width(80));
                EditorGUILayout.LabelField("Multiplier", EditorStyles.boldLabel, GUILayout.Width(80));
                EditorGUILayout.LabelField("Cumulative XP", EditorStyles.boldLabel, GUILayout.Width(120));
                EditorGUILayout.EndHorizontal();

                // Draw separator
                EditorGUILayout.Space(2);
                Rect separatorRect = EditorGUILayout.GetControlRect(false, 1);
                EditorGUI.DrawRect(separatorRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));
                EditorGUILayout.Space(2);

                // Calculate cumulative XP
                int cumulativeXP = startingExperience;

                // Draw table rows
                for (int i = 0; i < experienceRequirements.Count; i++)
                {
                    int level = startingLevel + i + 1;
                    int xpRequired = experienceRequirements[i];

                    // Skip levels beyond maxLevel
                    if (level > maxLevel)
                        break;

                    // Calculate increase from previous level
                    int previousXP = i > 0 ? experienceRequirements[i-1] : startingExperience;
                    int increase = xpRequired - previousXP;
                    float multiplier = (float)xpRequired / previousXP;

                    // Update cumulative XP
                    cumulativeXP += xpRequired;

                    // Highlight milestone levels (every 5 levels)
                    bool isMilestone = level % 5 == 0;
                    bool isMaxLevel = level == maxLevel;
                    GUIStyle rowStyle = (isMilestone || isMaxLevel) ? EditorStyles.boldLabel : EditorStyles.label;

                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField(level.ToString(), rowStyle, GUILayout.Width(50));
                    EditorGUILayout.LabelField(xpRequired.ToString("N0"), rowStyle, GUILayout.Width(100));
                    EditorGUILayout.LabelField(increase.ToString("N0"), rowStyle, GUILayout.Width(80));
                    EditorGUILayout.LabelField(multiplier.ToString("F3"), rowStyle, GUILayout.Width(80));
                    EditorGUILayout.LabelField(cumulativeXP.ToString("N0"), rowStyle, GUILayout.Width(120));
                    EditorGUILayout.EndHorizontal();

                    // Add separator after milestone levels or max level
                    if (isMilestone || isMaxLevel)
                    {
                        EditorGUILayout.Space(2);
                        separatorRect = EditorGUILayout.GetControlRect(false, 1);
                        EditorGUI.DrawRect(separatorRect, new Color(0.5f, 0.5f, 0.5f, 0.5f));
                        EditorGUILayout.Space(2);
                    }
                }

                EditorGUILayout.EndScrollView();
            }
            else
            {
                EditorGUILayout.HelpBox("No experience requirements calculated. Adjust your pattern and settings.", MessageType.Info);
            }

            EditorGUILayout.EndVertical();
            EditorGUILayout.Space(10);

            #endregion

            #region Action Buttons
            // Action buttons
            EditorGUILayout.BeginHorizontal();

            if (GUILayout.Button("Reset", GUILayout.Width(100)))
            {
                if (EditorUtility.DisplayDialog("Reset Pattern", "Are you sure you want to reset the pattern? This will clear all points.", "Reset", "Cancel"))
                {
                    // Reset the algorithm data using the pattern creator
                    patternCreator.ResetAlgorithmData(algorithmData);

                    // Update points from the algorithm data
                    points.Clear();
                    points.AddRange(algorithmData.points);

                    // Reset editing state
                    isEditingExistingPattern = false;
                    currentPatternPath = string.Empty;

                    // Update the data
                    UpdateDataFromPoints();

                    // Repaint the window
                    window.Repaint();
                }
            }

            if (GUILayout.Button("Load Pattern", GUILayout.Width(120)))
            {
                // Use the file operations helper to load a pattern
                fileOperations.LoadPattern();
            }

            if (GUILayout.Button(isEditingExistingPattern ? "Save Changes" : "Save Pattern", GUILayout.Width(120)))
            {
                // Create settings object for the current editor state
                var settings = new AlgorithmDesignerFileOperations.PatternEditorSettings
                {
                    yAxisScale = yAxisScale,
                    yAxisMin = yAxisMin,
                    yAxisMax = yAxisMax,
                    onePointPerLevel = onePointPerLevel,
                    autoAdjustPoints = autoAdjustPoints,
                    showGridLines = showGridLines,
                    showPointValues = showPointValues,
                    useCustomColors = useCustomColors,
                    customCurveColor = customCurveColor
                };

                // Save the settings
                fileOperations.SaveEditorSettings(currentPatternPath, settings);

                // Use the file operations helper to save the pattern
                fileOperations.SavePattern(algorithmData, isEditingExistingPattern, currentPatternPath);
            }

            GUILayout.FlexibleSpace();

            if (GUILayout.Button("Convert Coded Algorithms", GUILayout.Width(180)))
            {
                if (EditorUtility.DisplayDialog("Convert Coded Algorithms",
                    "This will find all coded algorithms in the project and convert them to scriptable objects. Continue?",
                    "Yes, Convert", "Cancel"))
                {
                    ConvertAllCodedAlgorithms();
                }
            }

            if (GUILayout.Button("Test Find Algorithms", GUILayout.Width(150)))
            {
                // Test finding coded algorithms
                var algorithms = fileOperations.FindCodedAlgorithmTypes();

                // Show results
                string message = $"Found {algorithms.Count} coded algorithm(s):\n\n";
                foreach (var type in algorithms)
                {
                    message += $"• {type.FullName}\n";
                }

                EditorUtility.DisplayDialog("Find Coded Algorithms", message, "OK");
            }

            EditorGUILayout.EndHorizontal();

            // Display current pattern info if editing an existing one
            if (isEditingExistingPattern)
            {
                EditorGUILayout.Space(5);
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Editing:", GUILayout.Width(50));
                EditorGUILayout.LabelField(System.IO.Path.GetFileNameWithoutExtension(currentPatternPath), EditorStyles.boldLabel);
                EditorGUILayout.EndHorizontal();
            }

            EditorGUILayout.Space(10); // Reduced from 20 to 10

            // End the vertical layout
            EditorGUILayout.EndVertical();
            #endregion
        }

        private void DrawGraph()
        {
            // Set display options for the graph renderer
            graphRenderer.SetDisplayOptions(showGridLines, showPointValues, useCustomColors, customCurveColor);
            graphRenderer.SetYAxisRange(yAxisMin, yAxisMax);

            // Get sorted points for drawing the curve
            var sortedPoints = GetSortedPoints();

            // Use the graph renderer to draw the graph
            graphRenderer.DrawGraph(graphRect, points, sortedPoints, selectedPointIndex);

            // Calculate the inner rect for input handling
            Rect innerRect = new(
                graphRect.x + graphPadding,
                graphRect.y + graphPadding,
                graphRect.width - (2 * graphPadding),
                graphRect.height - (2 * graphPadding)
            );

            // Handle input events using the input handler
            HandleDirectInputEvents(innerRect);
        }

        // These methods have been replaced by the AlgorithmDesignerGraphRenderer helper class

        // These methods have been replaced by the AlgorithmDesignerGraphRenderer helper class

        private void UpdateDataFromPoints()
        {
            if (algorithmData == null)
                return;

            // Use cached sorted points
            var sortedPoints = GetSortedPoints();

            // Update the algorithm's points
            algorithmData.points.Clear();
            foreach (var point in sortedPoints)
            {
                algorithmData.points.Add(point);
            }

            // Validate the data
            algorithmData.Validate();

            // Calculate experience requirements
            CalculateExperienceRequirements();
        }

        /// <summary>
        /// Calculates experience requirements using the current algorithm and settings
        /// </summary>
        private void CalculateExperienceRequirements()
        {
            if (algorithmData == null)
                return;

            // Check if recalculation is needed for performance
            if (!ShouldRecalculateData())
                return;

            // Update data calculator settings
            dataCalculator.StartingLevel = startingLevel;
            dataCalculator.MaxLevel = maxLevel;
            dataCalculator.LevelUpMultiplier = levelUpMultiplier;
            dataCalculator.StartingExperience = startingExperience;

            // Calculate experience requirements using the data calculator
            dataCalculator.CalculateExperienceRequirements(algorithmData);

            // Get the calculated data
            experienceRequirements = dataCalculator.ExperienceRequirements;

            // Update cache state
            UpdateCacheState();
        }

        /// <summary>
        /// Checks if data recalculation is needed based on changed parameters
        /// </summary>
        private bool ShouldRecalculateData()
        {
            if (needsDataRecalculation)
                return true;

            int currentPointsHash = GetPointsHash();

            return lastLevelUpMultiplier != levelUpMultiplier ||
                   lastStartingExperience != startingExperience ||
                   lastStartingLevel != startingLevel ||
                   lastMaxLevel != maxLevel ||
                   lastPointsHash != currentPointsHash;
        }

        /// <summary>
        /// Updates the cache state after recalculation
        /// </summary>
        private void UpdateCacheState()
        {
            lastLevelUpMultiplier = levelUpMultiplier;
            lastStartingExperience = startingExperience;
            lastStartingLevel = startingLevel;
            lastMaxLevel = maxLevel;
            lastPointsHash = GetPointsHash();
            needsDataRecalculation = false;
            needsPreviewRecalculation = true; // Preview needs update when data changes
        }

        /// <summary>
        /// Gets a hash of the current points for change detection
        /// </summary>
        private int GetPointsHash()
        {
            if (points == null || points.Count == 0)
                return 0;

            int hash = points.Count;
            foreach (var point in points)
            {
                hash = hash * 31 + point.GetHashCode();
            }
            return hash;
        }



        /// <summary>
        /// Converts the current points to have exactly one point per level
        /// </summary>
        private void ConvertToOnePointPerLevel()
        {
            if (points.Count == 0 || maxLevel <= startingLevel)
                return;

            // Use the pattern creator to convert to one point per level
            patternCreator.ConvertToOnePointPerLevel(points, startingLevel, maxLevel);

            // Update the points from the pattern creator
            points.Clear();
            points.AddRange(patternCreator.GetGeneratedPoints());

            // Update the algorithm data
            UpdateDataFromPoints();
        }



        // ResetPattern method is now public and defined in the Public Methods region

        /// <summary>
        /// Sets the Y-axis range and rescales existing points to fit the new range
        /// </summary>
        private void SetYAxisRange(float min, float max)
        {
            if (max <= min)
            {
                EditorUtility.DisplayDialog("Invalid Range", "Maximum value must be greater than minimum value.", "OK");
                return;
            }

            // Store the old range for scaling
            float oldRangeMin = yAxisMin;
            float oldRangeMax = yAxisMax;

            // Update the range
            yAxisMin = min;
            yAxisMax = max;

            // Update the pattern creator's Y-axis range
            patternCreator.SetYAxisRange(yAxisMin, yAxisMax);

            // Scale existing points to the new range
            RescalePointsToNewRange(oldRangeMin, oldRangeMax, yAxisMin, yAxisMax);

            // Update the algorithm data
            UpdateDataFromPoints();
            window.Repaint();
        }

        /// <summary>
        /// Rescales all points from the old Y-axis range to the new Y-axis range with boundary checking
        /// </summary>
        private void RescalePointsToNewRange(float oldMin, float oldMax, float newMin, float newMax)
        {
            if (points.Count == 0 || oldMax <= oldMin || newMax <= newMin)
                return;

            // Use the pattern creator to rescale the points
            List<Vector2> rescaledPoints = patternCreator.RescalePointsToNewRange(points, oldMin, oldMax, newMin, newMax);

            // Update our points list with the rescaled points
            points.Clear();
            points.AddRange(rescaledPoints);
        }

        // SavePattern method is now public and defined in the Public Methods region



        /// <summary>
        /// Draws a preview of the algorithm's experience curve and raw formula curve using the LevelingCurvePreview helper
        /// </summary>
        private void DrawAlgorithmPreview(ILevelingAlgorithm algorithm, float levelUpMultiplier, int startingExperience, int startingLevel, int maxLevel, float availableWidth)
        {
            // Initialize the curve preview if needed
            curvePreview ??= new LevelingCurvePreview();

            // Use the curve preview helper to draw the graphs
            curvePreview.DrawCurvePreview(
                algorithm,
                levelUpMultiplier,
                null, // No comparison algorithm
                startingExperience,
                startingLevel,
                maxLevel,
                availableWidth,
                "Experience Requirements by Level",
                "Level-Up Multiplier by Level"
            );

            // Store the experience requirements for the detailed stats
            experienceRequirements = algorithm.CalculateRequirementCurve(startingExperience, startingLevel, maxLevel, levelUpMultiplier);

            EditorGUILayout.Space(10); // Reduced spacing from 20 to 10

            // Experience Rewards Section
            EditorGUILayout.LabelField("Experience Rewards Preview", subHeaderStyle);
            EditorGUILayout.Space(5);

            // Load the rewards data
            ExperienceRewardsData rewardsData = Resources.Load<ExperienceRewardsData>("ExperienceRewardsData");
            if (rewardsData != null && rewardsData.rewardCategories.Count > 0)
            {
                // Create a table for the rewards with its own scrollview
                EditorGUILayout.BeginVertical(EditorStyles.helpBox);

                // Create a scrollview for the rewards table
                float rewardsTableHeight = Mathf.Min(300, rewardsData.rewardCategories.Count * 25 + 50); // Height based on number of categories
                experienceRewardsScrollPosition = EditorGUILayout.BeginScrollView(experienceRewardsScrollPosition, GUILayout.Height(rewardsTableHeight));

                // Table header
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField("Reward Category", EditorStyles.boldLabel, GUILayout.Width(150));

                // Create column headers for level ranges
                int rangeSize = 5; // Show 5 levels per column
                int numColumns = Mathf.Min(6, Mathf.CeilToInt((float)(maxLevel - startingLevel + 1) / rangeSize));

                for (int i = 0; i < numColumns; i++)
                {
                    int startRange = startingLevel + i * rangeSize;
                    int endRange = Mathf.Min(startRange + rangeSize - 1, maxLevel);

                    // Skip ranges that are beyond maxLevel
                    if (startRange > maxLevel)
                        break;

                    EditorGUILayout.LabelField($"Levels {startRange}-{endRange}", EditorStyles.boldLabel, GUILayout.Width(100));
                }

                EditorGUILayout.EndHorizontal();

                // Draw a separator line
                EditorGUILayout.Space(2);
                Rect rect = EditorGUILayout.GetControlRect(false, 1);
                EditorGUI.DrawRect(rect, new Color(0.5f, 0.5f, 0.5f, 1));
                EditorGUILayout.Space(2);

                // Show rewards for each category
                foreach (var category in rewardsData.rewardCategories)
                {
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField(category.name, GUILayout.Width(150));

                    // Calculate and display rewards for each level range
                    for (int i = 0; i < numColumns; i++)
                    {
                        int startRange = startingLevel + i * rangeSize;
                        int endRange = Mathf.Min(startRange + rangeSize - 1, maxLevel);

                        // Skip ranges that are beyond maxLevel
                        if (startRange > maxLevel)
                            break;

                        // Calculate the total XP needed for this level range
                        int totalXpForRange = 0;
                        int countLevels = 0;

                        // Sum up XP for all levels in this range
                        for (int level = startRange; level <= endRange; level++)
                        {
                            int baseLevel = level - startingLevel - 1; // -1 because experienceRequirements is 0-indexed
                            if (baseLevel >= 0 && baseLevel < experienceRequirements.Count)
                            {
                                totalXpForRange += experienceRequirements[baseLevel];
                                countLevels++;
                            }
                        }

                        // Calculate how many rewards of this type would be needed for this range
                        int reward = 0;
                        if (countLevels > 0 && category.multiplier > 0)
                        {
                            // Calculate the average XP per level in this range
                            float avgXpPerLevel = totalXpForRange / (float)countLevels;

                            // Calculate how many of this reward type are needed
                            // (total XP needed / XP per reward)
                            reward = Mathf.RoundToInt(totalXpForRange / (avgXpPerLevel * category.multiplier));
                        }

                        // Display the reward
                        EditorGUILayout.LabelField(reward.ToString(), GUILayout.Width(100));
                    }

                    EditorGUILayout.EndHorizontal();
                }

                // End the scrollview for the rewards table
                EditorGUILayout.EndScrollView();
                EditorGUILayout.EndVertical();

                // Add a note about how rewards are calculated
                EditorGUILayout.Space(5);
                EditorGUILayout.HelpBox(
                    "This table shows how many rewards of each type are needed to level up through the specified range. " +
                    "For example, if Epic Quest shows '30' for levels 1-5, it means you need about 30 Epic Quests to progress from level 1 to 5. " +
                    "Reward percentages can be customized in the Experience Rewards tab.",
                    MessageType.Info);
            }
            else
            {
                EditorGUILayout.HelpBox("Experience rewards data not found. Please go to the Experience Rewards tab to set up reward categories.", MessageType.Warning);
            }

            // Detailed Stats Section
            EditorGUILayout.Space(10);
            EditorGUILayout.LabelField("Detailed Level Statistics", subHeaderStyle);
            EditorGUILayout.Space(5);

            // Create a scrollable area for the detailed stats
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);

            // Calculate total experience required
            int totalExperience = experienceRequirements.Sum();

            // Display summary statistics
            EditorGUILayout.LabelField("Summary Statistics", EditorStyles.boldLabel);
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Total Experience Required:", GUILayout.Width(200));
            EditorGUILayout.LabelField(totalExperience.ToString("N0"), EditorStyles.boldLabel);
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Average Experience Per Level:", GUILayout.Width(200));
            EditorGUILayout.LabelField((totalExperience / experienceRequirements.Count).ToString("N0"), EditorStyles.boldLabel);
            EditorGUILayout.EndHorizontal();

            // Find min and max experience jumps
            int minJump = int.MaxValue;
            int maxJump = 0;
            int minJumpLevel = 0;
            int maxJumpLevel = 0;

            for (int i = 1; i < experienceRequirements.Count; i++)
            {
                int jump = experienceRequirements[i] - experienceRequirements[i-1];
                if (jump < minJump)
                {
                    minJump = jump;
                    minJumpLevel = startingLevel + i;
                }
                if (jump > maxJump)
                {
                    maxJump = jump;
                    maxJumpLevel = startingLevel + i;
                }
            }

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Smallest Level Jump:", GUILayout.Width(200));
            EditorGUILayout.LabelField($"{minJump:N0} (Level {minJumpLevel})", EditorStyles.boldLabel);
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Largest Level Jump:", GUILayout.Width(200));
            EditorGUILayout.LabelField($"{maxJump:N0} (Level {maxJumpLevel})", EditorStyles.boldLabel);
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.Space(10);

            // Create a table for level-by-level stats
            EditorGUILayout.LabelField("Level-by-Level Requirements", EditorStyles.boldLabel);
            EditorGUILayout.Space(5);

            // Pagination controls for performance
            int totalRows = experienceRequirements.Count + 1; // +1 for starting level
            int maxPages = Mathf.CeilToInt((float)totalRows / MAX_TABLE_ROWS);
            int currentPage = Mathf.FloorToInt((float)tableStartIndex / MAX_TABLE_ROWS);

            if (maxPages > 1)
            {
                EditorGUILayout.BeginHorizontal();
                EditorGUILayout.LabelField($"Page {currentPage + 1} of {maxPages}", GUILayout.Width(100));

                if (GUILayout.Button("Previous", GUILayout.Width(80)) && currentPage > 0)
                {
                    tableStartIndex = Mathf.Max(0, tableStartIndex - MAX_TABLE_ROWS);
                }

                if (GUILayout.Button("Next", GUILayout.Width(80)) && currentPage < maxPages - 1)
                {
                    tableStartIndex = Mathf.Min(totalRows - MAX_TABLE_ROWS, tableStartIndex + MAX_TABLE_ROWS);
                }

                EditorGUILayout.EndHorizontal();
                EditorGUILayout.Space(5);
            }

            // Table header
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField("Level", EditorStyles.boldLabel, GUILayout.Width(50));
            EditorGUILayout.LabelField("XP Required", EditorStyles.boldLabel, GUILayout.Width(100));
            EditorGUILayout.LabelField("Increase", EditorStyles.boldLabel, GUILayout.Width(100));
            EditorGUILayout.LabelField("% Increase", EditorStyles.boldLabel, GUILayout.Width(100));
            EditorGUILayout.LabelField("Cumulative XP", EditorStyles.boldLabel, GUILayout.Width(120));
            EditorGUILayout.EndHorizontal();

            // Create a fixed-height area for the table (no scrollview needed with pagination)
            EditorGUILayout.BeginVertical(GUILayout.Height(MAX_TABLE_ROWS * 20));

            // Calculate cumulative XP up to the start of the current page
            // Cumulative XP represents the total XP needed to reach each level
            int cumulativeXP = startingExperience;
            for (int i = 0; i < tableStartIndex && i < experienceRequirements.Count; i++)
            {
                // For cumulative XP, we add the XP required to level up FROM the previous level
                // This represents the total XP pool needed to reach that level
                cumulativeXP += experienceRequirements[i];
            }

            // Render only the visible rows for performance
            int endIndex = Mathf.Min(tableStartIndex + MAX_TABLE_ROWS, totalRows);
            int rowsRendered = 0;

            for (int i = tableStartIndex; i < endIndex && rowsRendered < MAX_TABLE_ROWS; i++)
            {
                if (i == 0)
                {
                    // First row for starting level
                    EditorGUILayout.BeginHorizontal();
                    EditorGUILayout.LabelField(startingLevel.ToString(), GUILayout.Width(50));
                    EditorGUILayout.LabelField(startingExperience.ToString(), GUILayout.Width(100));
                    EditorGUILayout.LabelField("-", GUILayout.Width(100));
                    EditorGUILayout.LabelField("-", GUILayout.Width(100));
                    EditorGUILayout.LabelField(startingExperience.ToString(), GUILayout.Width(120));
                    EditorGUILayout.EndHorizontal();
                }
                else
                {
                    // Regular level rows
                    int expIndex = i - 1; // Adjust for starting level offset
                    if (expIndex >= 0 && expIndex < experienceRequirements.Count)
                    {
                        int level = startingLevel + expIndex + 1;

                        // Skip levels beyond maxLevel
                        if (level > maxLevel)
                            break;

                        int xpRequired = experienceRequirements[expIndex];

                        // Calculate cumulative XP properly - this should be the total XP needed to reach this level
                        // We need to sum all XP requirements from level 1 to this level
                        int levelCumulativeXP = startingExperience;
                        for (int j = 0; j <= expIndex; j++)
                        {
                            levelCumulativeXP += experienceRequirements[j];
                        }

                        EditorGUILayout.BeginHorizontal();
                        EditorGUILayout.LabelField(level.ToString(), GUILayout.Width(50));
                        EditorGUILayout.LabelField(xpRequired.ToString("N0"), GUILayout.Width(100));

                        // Calculate increase from previous level
                        if (expIndex > 0)
                        {
                            int previousXP = experienceRequirements[expIndex-1];
                            int increase = xpRequired - previousXP;
                            float percentIncrease = ((float)xpRequired / previousXP - 1.0f) * 100f;
                            EditorGUILayout.LabelField(increase.ToString("N0"), GUILayout.Width(100));
                            EditorGUILayout.LabelField(percentIncrease.ToString("F1") + "%", GUILayout.Width(100));
                        }
                        else
                        {
                            // For the first level, show increase from starting XP
                            EditorGUILayout.LabelField(xpRequired.ToString("N0"), GUILayout.Width(100));
                            EditorGUILayout.LabelField("N/A", GUILayout.Width(100));
                        }

                        EditorGUILayout.LabelField(levelCumulativeXP.ToString("N0"), GUILayout.Width(120));
                        EditorGUILayout.EndHorizontal();
                    }
                }
                rowsRendered++;
            }

            EditorGUILayout.EndVertical(); // End the table area
            EditorGUILayout.EndVertical(); // End the helpBox
        }

        #region Advanced Pattern Creation Methods

        /// <summary>
        /// Applies a predefined curve template to the points using the PatternCreator helper
        /// </summary>
        private void ApplyCurveTemplate(CurveTemplate template)
        {
            // Set the Y-axis range for the pattern creator
            patternCreator.SetYAxisRange(yAxisMin, yAxisMax);

            // Set the template intensity
            patternCreator.TemplateIntensity = templateIntensity;

            // Number of points to generate for the template
            int numPoints = onePointPerLevel ? (maxLevel - startingLevel + 1) : 20;

            // Convert our CurveTemplate enum to the PatternCreator's CurveTemplate enum
            var patternCreatorTemplate = (AlgorithmDesignerPatternCreator.CurveTemplate)(int)template;

            // Apply the template using the pattern creator
            patternCreator.ApplyTemplate(patternCreatorTemplate, templateIntensity, numPoints);

            // Get the generated points from the pattern creator
            List<Vector2> generatedPoints = patternCreator.GetGeneratedPoints();

            // Only update if we actually got points back
            if (generatedPoints != null && generatedPoints.Count > 0)
            {
                // Update the points from the pattern creator
                points.Clear();
                points.AddRange(generatedPoints);

                // Update the algorithm data
                UpdateDataFromPoints();

                // Repaint the window to show the changes
                window.Repaint();
            }
            else
            {
                Debug.LogError("Failed to generate points from template: " + template);
            }
        }
        #endregion
    }
}
