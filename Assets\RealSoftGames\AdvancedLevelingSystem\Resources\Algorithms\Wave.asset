%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2fbb257193b588d45b350dddd3c20989, type: 3}
  m_Name: Wave
  m_EditorClassIdentifier: 
  algorithmType: 1
  uniqueID: 23392563-c18a-47eb-a219-c198a7ba72d8
  algorithmName: Wave
  description: Creates a smooth wave pattern with gradually increasing amplitude,
    like ocean waves that grow larger over time.
  formulaExplanation: 'Formula: Uses sine waves with increasing amplitude and frequency


    Creates
    a wave pattern where both the height and frequency of waves increase as levels
    progress, creating a more dynamic and challenging progression.'
  difficultyRating: {fileID: -3552319746794289848, guid: db1983e304258374fbc563bb265fc593, type: 2}
  points: []
  interpolationMethod: 0
  baseMultiplier: 1.1
  levelMultiplierFactor: 0.01
  useSineWave: 0
  sineAmplitude: 0.1
  sinePeriod: 5
  useHeartbeat: 0
  heartbeatAmplitude: 0.15
  heartbeatPeriod: 10
  useZigzag: 0
  zigzagAmplitude: 0.1
  zigzagPeriod: 3
  useRandomFluctuations: 0
  randomAmplitude: 0.05
  randomSeed: 12345
  cachedStartingExperience: 250
  cachedStartingLevel: 1
  cachedMaxLevel: 50
  cachedLevelUpMultiplier: 1.1
  cachedRequirementCurvePoints: []
  cachedRawFormulaCurvePoints: []
  cachedRequirementCurve: 
  cachedRawFormulaCurve: []
  baseFrequency: 5
  frequencyGrowth: 3
  baseAmplitude: 0.05
  amplitudeGrowth: 4
  zeroBaseMultiplier: 1.05
