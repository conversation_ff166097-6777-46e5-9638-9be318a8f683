using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Extension methods for the Diminishing Returns algorithm
    /// </summary>
    public static class DiminishingReturnsAlgorithmExtension
    {
        // Default parameters
        private const float DefaultDiminishingDenominator = 10f;
        private const float DefaultDiminishingScaleFactor = 0.5f;
        private const float DefaultZeroBaseMultiplier = 1.1f;

        /// <summary>
        /// Calculates the next experience requirement using the diminishing returns formula method
        /// </summary>
        public static int CalculateDiminishingReturnsRequirement(this LevelingAlgorithmBase algorithm, int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);

            // Calculate the diminishing returns factor
            // This creates a curve that approaches 0.5 as levels increase
            float diminishingFactor = currentLevel / (currentLevel + DefaultDiminishingDenominator);

            // Calculate the actual multiplier with diminishing returns
            float actualMultiplier;

            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure diminishing returns pattern
                // Start at base value and decrease with diminishing returns
                actualMultiplier = DefaultZeroBaseMultiplier * (1f - diminishingFactor * DefaultDiminishingScaleFactor);
            }
            else
            {
                // Apply the diminishing returns to the effective multiplier
                actualMultiplier = effectiveMultiplier * (1f - diminishingFactor * DefaultDiminishingScaleFactor);
            }

            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);

            // Allow negative multipliers to reduce XP requirements (easier levels)
            // Only enforce a minimum of 1 XP to prevent zero or negative XP requirements
            return Mathf.Max(nextRequirement, 1);
        }

        /// <summary>
        /// Calculates raw formula values for visualization using the diminishing returns formula method
        /// </summary>
        public static List<float> CalculateDiminishingReturnsRawFormulaCurve(this LevelingAlgorithmBase algorithm, int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            var rawValues = new List<float>();

            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = algorithm.GetEffectiveMultiplier(levelUpMultiplier);

            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the diminishing returns factor
                float diminishingFactor = level / (level + DefaultDiminishingDenominator);

                // Calculate the actual multiplier with diminishing returns
                float rawValue;

                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure diminishing returns pattern
                    rawValue = DefaultZeroBaseMultiplier * (1f - diminishingFactor * DefaultDiminishingScaleFactor);
                }
                else
                {
                    // Apply the diminishing returns to the effective multiplier
                    rawValue = effectiveMultiplier * (1f - diminishingFactor * DefaultDiminishingScaleFactor);
                }

                // Add to the list
                rawValues.Add(rawValue);
            }

            return rawValues;
        }
    }
}
