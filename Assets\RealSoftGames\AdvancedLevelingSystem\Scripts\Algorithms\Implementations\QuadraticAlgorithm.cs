using UnityEngine;
using System.Collections.Generic;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Quadratic leveling algorithm
    /// </summary>
    [CreateAssetMenu(fileName = "Quadratic Algorithm", menuName = "RealSoftGames/Advanced Leveling System/Algorithms/Quadratic Algorithm", order = 109)]
    public class QuadraticAlgorithm : LevelingAlgorithmBase
    {
        [Tooltip("Base multiplier when levelUpMultiplier is effectively zero")]
        [Range(1.01f, 1.2f)]
        public float zeroBaseMultiplier = 1.05f;

        [Tooltip("Coefficient for the quadratic term")]
        [Range(0.001f, 0.01f)]
        public float quadraticCoefficient = 0.005f;

        /// <summary>
        /// Called when the scriptable object is created
        /// </summary>
        protected override void OnEnable()
        {
            // Set default values
            algorithmType = AlgorithmEnums.AlgorithmType.CodedFormula;
            algorithmName = "Quadratic";
            description = "Experience requirements grow with the square of the level, creating a steep curve.";
            formulaExplanation = "Formula: requiredExp = requiredExp * (levelUpMultiplier * (1 + 0.005 * level^2))\n\nExperience requirements grow with the square of the level, creating a steep curve.";

            // Call base implementation
            base.OnEnable();
        }

        /// <summary>
        /// Calculates the next experience requirement using the quadratic formula method
        /// </summary>
        protected override int CalculateNextRequirementCodedFormula(int currentExperience, int currentLevel, float levelUpMultiplier, int startingLevel, int maxLevel)
        {
            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);

            // Calculate the quadratic growth factor
            // This creates a curve that increases the multiplier as the square of the level
            float growthFactor = 1f + quadraticCoefficient * (currentLevel * currentLevel);

            // Calculate the actual multiplier with quadratic growth
            float actualMultiplier;

            if (levelUpMultiplier <= 0.01f)
            {
                // When levelUpMultiplier is effectively zero, use a pure quadratic pattern
                // with a smaller base to avoid excessive growth
                actualMultiplier = zeroBaseMultiplier * growthFactor;
            }
            else
            {
                // Apply the quadratic growth to the effective multiplier
                actualMultiplier = effectiveMultiplier * growthFactor;
            }

            // Calculate the next requirement
            int nextRequirement = Mathf.RoundToInt(currentExperience * actualMultiplier);

            // Allow negative multipliers to reduce XP requirements (easier levels)
            // Only enforce a minimum of 1 XP to prevent zero or negative XP requirements
            return Mathf.Max(nextRequirement, 1);
        }

        /// <summary>
        /// Calculates raw formula values for visualization using the quadratic formula method
        /// </summary>
        protected override List<float> CalculateRawFormulaCurveCodedFormula(int startingLevel, int maxLevel, float levelUpMultiplier)
        {
            List<float> rawValues = new List<float>();

            // Get an effective multiplier that respects the levelUpMultiplier setting
            float effectiveMultiplier = GetEffectiveMultiplier(levelUpMultiplier);

            // Calculate the raw multiplier for each level
            for (int level = startingLevel; level <= maxLevel; level++)
            {
                // Calculate the quadratic growth factor
                float growthFactor = 1f + quadraticCoefficient * (level * level);

                // Calculate the actual multiplier with quadratic growth
                float rawValue;

                if (levelUpMultiplier <= 0.01f)
                {
                    // When levelUpMultiplier is effectively zero, use a pure quadratic pattern
                    rawValue = zeroBaseMultiplier * growthFactor;
                }
                else
                {
                    // Apply the quadratic growth to the effective multiplier
                    rawValue = effectiveMultiplier * growthFactor;
                }

                // Add to the list
                rawValues.Add(rawValue);
            }

            return rawValues;
        }
    }
}
