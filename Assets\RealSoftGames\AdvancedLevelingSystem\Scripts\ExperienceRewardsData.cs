using System.Collections.Generic;
using UnityEngine;

namespace RealSoftGames.AdvancedLevelingSystem
{
    public enum RewardType
    {
        Percentage,
        HardValue
    }

    [CreateAssetMenu(fileName = "ExperienceRewardsData", menuName = "RealSoftGames/Leveling System/Experience Rewards Data")]
    public class ExperienceRewardsData : ScriptableObject
    {
        [System.Serializable]
        public class RewardCategory
        {
            public string name;
            public float multiplier; // Used for percentage-based rewards
            public int hardValue;    // Used for hard value-based rewards
            public RewardType rewardType = RewardType.Percentage;
        }

        public List<RewardCategory> rewardCategories = new List<RewardCategory>();

        // Algorithm to use for experience calculations
        public LevelingAlgorithmBase selectedAlgorithm;

        // Reward system settings
        public RewardType defaultRewardType = RewardType.Percentage;

        public float levelUpMultiplier = 1.1f;
        public int startingExperience = 250;
        public int startingLevel = 1;
        public int maxLevel = 50;
        public int levelRangeSize = 5;
        public bool showDetailedBreakdown = true;
        public bool showLevelCurvePreview = true; // New setting for collapsible curve preview

        // Note: Reward scaling is now directly based on the algorithm and levelUpMultiplier
        // No separate scaling settings needed
    }
}
