using UnityEditor;
using UnityEngine;
using System;
using System.Collections.Generic;
using System.Linq;
using RealSoftGames.AdvancedLevelingSystem;

namespace RealSoftGames.AdvancedLevelingSystem
{
    /// <summary>
    /// Handles data calculations for the algorithm designer
    /// </summary>
    public class AlgorithmDesignerDataCalculator
    {
        // Experience requirements
        private List<int> experienceRequirements = new List<int>();
        private List<float> rawFormulaValues = new List<float>();

        // Preview settings
        private int startingLevel = 1;
        private int maxLevel = 30;
        private float levelUpMultiplier = 1.1f;
        private int startingExperience = 250;

        // Events
        public event Action<List<int>, List<float>> OnDataCalculated;

        // Properties
        public List<int> ExperienceRequirements => experienceRequirements;
        public List<float> RawFormulaValues => rawFormulaValues;

        public int StartingLevel
        {
            get => startingLevel;
            set => startingLevel = value;
        }

        public int MaxLevel
        {
            get => maxLevel;
            set => maxLevel = value;
        }

        public float LevelUpMultiplier
        {
            get => levelUpMultiplier;
            set => levelUpMultiplier = value;
        }

        public int StartingExperience
        {
            get => startingExperience;
            set => startingExperience = value;
        }

        /// <summary>
        /// Calculates experience requirements based on the algorithm data
        /// </summary>
        public void CalculateExperienceRequirements(LevelingAlgorithmBase algorithmData)
        {
            if (algorithmData == null)
                return;

            try
            {
                // Clear previous data
                experienceRequirements.Clear();
                rawFormulaValues.Clear();

                // Calculate the number of levels
                int numLevels = maxLevel - startingLevel + 1;

                // Get the sorted points from the algorithm
                var sortedPoints = algorithmData.points.OrderBy(p => p.x).ToList();

                // If we have fewer points than levels, enhance the interpolation
                if (sortedPoints.Count < numLevels && sortedPoints.Count > 1)
                {
                    sortedPoints = EnhancePointsForSmoothProgression(sortedPoints, numLevels);
                }

                // Calculate raw formula values
                for (int i = 0; i < numLevels; i++)
                {
                    // Calculate the normalized progress (0 to 1)
                    float progress = i / (float)Mathf.Max(1, numLevels - 1);

                    // Interpolate the Y value from the points
                    float y = InterpolateYValue(sortedPoints, progress);

                    // Store the raw formula value
                    rawFormulaValues.Add(y);


                }

                // Calculate experience requirements
                int previousXP = startingExperience;

                for (int i = 0; i < numLevels; i++)
                {
                    // Get the raw formula value for this level
                    float rawValue = rawFormulaValues[i];

                    // Apply the level up multiplier if enabled
                    float effectiveMultiplier = (levelUpMultiplier != 0) ? rawValue * levelUpMultiplier : rawValue;

                    // Ensure the multiplier provides meaningful progression
                    // Calculate a dynamic minimum based on level progression
                    float levelProgress = (float)i / (numLevels - 1); // 0 to 1
                    float dynamicMinimum = 1.001f + (levelProgress * 0.009f); // 0.1% to 1% increase

                    // Apply minimum increase if needed, but respect user's curve shape
                    if (effectiveMultiplier < dynamicMinimum)
                    {
                        effectiveMultiplier = dynamicMinimum;
                    }

                    // Calculate the experience required for this level
                    int xpRequired = Mathf.RoundToInt(previousXP * effectiveMultiplier);

                    // Ensure a minimum increase (at least 1 XP more than previous)
                    xpRequired = Mathf.Max(xpRequired, previousXP + 1);

                    // Store the experience requirement
                    experienceRequirements.Add(xpRequired);

                    // Update the previous XP for the next level
                    previousXP = xpRequired;
                }

                // Notify listeners
                OnDataCalculated?.Invoke(experienceRequirements, rawFormulaValues);
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error calculating experience requirements: {ex.Message}");
            }
        }

        /// <summary>
        /// Interpolates a Y value from a list of points at a given X coordinate
        /// </summary>
        private float InterpolateYValue(List<Vector2> points, float x)
        {
            if (points.Count == 0)
                return 1.0f;

            if (points.Count == 1)
                return points[0].y;

            // Find the two points that bracket the X coordinate
            for (int i = 0; i < points.Count - 1; i++)
            {
                if (x >= points[i].x && x <= points[i + 1].x)
                {
                    // Calculate the interpolation factor
                    float t = (x - points[i].x) / (points[i + 1].x - points[i].x);

                    // Interpolate the Y value
                    return Mathf.Lerp(points[i].y, points[i + 1].y, t);
                }
            }

            // If X is outside the range, return the Y value of the closest point
            if (x < points[0].x)
                return points[0].y;
            else
                return points[points.Count - 1].y;
        }

        /// <summary>
        /// Gets the cumulative experience required to reach a specific level
        /// </summary>
        public int GetCumulativeExperience(int level)
        {
            if (level < startingLevel || level > maxLevel)
                return 0;

            int cumulativeXP = 0;

            // Add up all the experience requirements up to the specified level
            for (int i = startingLevel; i < level; i++)
            {
                int index = i - startingLevel;
                if (index >= 0 && index < experienceRequirements.Count)
                {
                    cumulativeXP += experienceRequirements[index];
                }
            }

            return cumulativeXP;
        }

        /// <summary>
        /// Gets the experience increase percentage between two levels
        /// </summary>
        public float GetExperienceIncreasePercentage(int level)
        {
            if (level <= startingLevel || level > maxLevel)
                return 0;

            int index = level - startingLevel;
            int prevIndex = index - 1;

            if (prevIndex < 0 || index >= experienceRequirements.Count)
                return 0;

            int currentXP = experienceRequirements[index];
            int previousXP = (prevIndex == -1) ? startingExperience : experienceRequirements[prevIndex];

            if (previousXP == 0)
                return 0;

            return ((float)currentXP / previousXP - 1.0f) * 100f;
        }

        /// <summary>
        /// Gets the effective multiplier for a specific level
        /// </summary>
        public float GetEffectiveMultiplier(int level)
        {
            if (level <= startingLevel || level > maxLevel)
                return 0;

            int index = level - startingLevel;
            int prevIndex = index - 1;

            if (prevIndex < 0 || index >= experienceRequirements.Count)
                return 0;

            int currentXP = experienceRequirements[index];
            int previousXP = (prevIndex == -1) ? startingExperience : experienceRequirements[prevIndex];

            if (previousXP == 0)
                return 0;

            return currentXP / (float)previousXP;
        }

        /// <summary>
        /// Enhances a sparse set of points to create smoother progression by adding intermediate points
        /// </summary>
        private List<Vector2> EnhancePointsForSmoothProgression(List<Vector2> originalPoints, int targetLevels)
        {
            if (originalPoints.Count < 2)
                return originalPoints;

            var enhancedPoints = new List<Vector2>();

            // Calculate how many intermediate points to add between each pair
            int totalSegments = originalPoints.Count - 1;
            int pointsPerSegment = Mathf.Max(2, targetLevels / totalSegments / 2); // At least 2 points per segment

            for (int i = 0; i < originalPoints.Count - 1; i++)
            {
                Vector2 startPoint = originalPoints[i];
                Vector2 endPoint = originalPoints[i + 1];

                // Add the start point
                enhancedPoints.Add(startPoint);

                // Add intermediate points to create smoother progression
                for (int j = 1; j < pointsPerSegment; j++)
                {
                    float t = (float)j / pointsPerSegment;
                    Vector2 intermediatePoint = Vector2.Lerp(startPoint, endPoint, t);

                    // Add slight variation to prevent completely flat areas
                    if (Mathf.Approximately(startPoint.y, endPoint.y))
                    {
                        // For flat segments, add a very small progressive increase
                        float progressiveIncrease = t * 0.01f; // 1% total increase across the segment
                        intermediatePoint.y += progressiveIncrease;
                    }

                    enhancedPoints.Add(intermediatePoint);
                }
            }

            // Add the final point
            enhancedPoints.Add(originalPoints[originalPoints.Count - 1]);

            return enhancedPoints;
        }
    }
}
